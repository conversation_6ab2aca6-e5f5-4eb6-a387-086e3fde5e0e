// 测试 SQLite 集成
require('dotenv').config();
const SlackAuthDatabase = require('./src/database/slackAuth');
const axios = require('axios');

async function testSQLiteIntegration() {
  console.log('🗄️ 测试 SQLite 集成');
  console.log('=' .repeat(60));

  const db = new SlackAuthDatabase();
  
  try {
    // 等待数据库初始化完成
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 1. 测试保存工作区鉴权信息
    console.log('1️⃣ 测试保存工作区鉴权信息...');
    
    const mockAuthData = {
      team_id: 'T059DMNT0SW',
      team_name: '大白鹅',
      bot_user_id: 'B098JR4NGTA',
      bot_user_access_token: 'xoxb-test-token-12345',
      authed_user_id: 'U053CTYEARZ',
      scope: 'chat:write,im:write,users:read,channels:read',
      installed_at: new Date().toISOString()
    };

    await db.saveWorkspaceAuth(mockAuthData);
    console.log('✅ 工作区鉴权信息保存成功');

    // 2. 测试根据团队ID获取工作区信息
    console.log('');
    console.log('2️⃣ 测试根据团队ID获取工作区信息...');
    
    const workspace = await db.getWorkspaceByTeamId('T059DMNT0SW');
    if (workspace) {
      console.log('✅ 工作区信息获取成功:');
      console.log('   团队名:', workspace.team_name);
      console.log('   Bot用户ID:', workspace.bot_user_id);
      console.log('   权限范围:', workspace.scope);
    } else {
      console.log('❌ 未找到工作区信息');
    }

    // 3. 测试保存用户映射
    console.log('');
    console.log('3️⃣ 测试保存用户映射...');
    
    await db.saveUserMapping('U0983BEK1HQ', 'T059DMNT0SW', 'D097W5SPBLM');
    console.log('✅ 用户映射保存成功');

    // 4. 测试根据用户ID获取工作区信息
    console.log('');
    console.log('4️⃣ 测试根据用户ID获取工作区信息...');
    
    const userWorkspace = await db.getWorkspaceByUserId('U0983BEK1HQ');
    if (userWorkspace) {
      console.log('✅ 根据用户ID获取工作区信息成功:');
      console.log('   团队名:', userWorkspace.team_name);
      console.log('   Bot Token存在:', !!userWorkspace.bot_user_access_token);
    } else {
      console.log('❌ 未找到用户对应的工作区信息');
    }

    // 5. 测试获取Bot Token
    console.log('');
    console.log('5️⃣ 测试获取Bot Token...');
    
    const tokenByTeam = await db.getBotTokenForTeam('T059DMNT0SW');
    const tokenByUser = await db.getBotTokenForUser('U0983BEK1HQ');
    
    console.log('✅ Token获取测试:');
    console.log('   根据团队ID获取:', tokenByTeam ? '成功' : '失败');
    console.log('   根据用户ID获取:', tokenByUser ? '成功' : '失败');
    console.log('   Token匹配:', tokenByTeam === tokenByUser ? '✅' : '❌');

    // 6. 测试获取所有工作区
    console.log('');
    console.log('6️⃣ 测试获取所有工作区...');
    
    const allWorkspaces = await db.getAllWorkspaces();
    console.log('✅ 工作区列表获取成功:');
    console.log('   总数:', allWorkspaces.length);
    allWorkspaces.forEach((ws, index) => {
      console.log(`   ${index + 1}. ${ws.team_name} (${ws.team_id}) - Token: ${ws.has_token ? '✅' : '❌'}`);
    });

    // 7. 测试服务器API端点
    console.log('');
    console.log('7️⃣ 测试服务器API端点...');
    
    const serverPort = process.env.PORT || 3002;
    
    try {
      const response = await axios.get(`http://localhost:${serverPort}/slack/workspaces`);
      console.log('✅ 服务器API测试成功:');
      console.log('   工作区数量:', response.data.total_workspaces);
      console.log('   API响应正常:', response.status === 200 ? '✅' : '❌');
    } catch (apiError) {
      console.log('❌ 服务器API测试失败:', apiError.message);
      if (apiError.code === 'ECONNREFUSED') {
        console.log('   💡 请确保服务器正在运行: npm start');
      }
    }

    // 8. 模拟消息发送场景
    console.log('');
    console.log('8️⃣ 模拟消息发送场景...');
    
    // 模拟根据用户ID获取Token的场景
    const userId = 'U0983BEK1HQ';
    const userToken = await db.getBotTokenForUser(userId);
    
    if (userToken) {
      console.log('✅ 消息发送场景模拟成功:');
      console.log('   用户ID:', userId);
      console.log('   获取到Token:', userToken.substring(0, 20) + '...');
      console.log('   可以发送消息:', '✅');
    } else {
      console.log('❌ 消息发送场景模拟失败: 未找到用户对应的Token');
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  } finally {
    // 关闭数据库连接
    db.close();
  }

  console.log('');
  console.log('📊 SQLite 集成测试总结');
  console.log('=' .repeat(60));
  console.log('✅ 完成的功能:');
  console.log('   1. 工作区鉴权信息持久化存储');
  console.log('   2. 用户和工作区映射关系管理');
  console.log('   3. 根据用户ID智能获取Bot Token');
  console.log('   4. 根据团队ID获取Bot Token');
  console.log('   5. 工作区列表管理和查看');
  console.log('');
  console.log('🎯 使用流程:');
  console.log('   1. OAuth授权 → 自动保存到SQLite');
  console.log('   2. 用户发消息 → 自动保存用户映射');
  console.log('   3. 发送回复 → 根据用户ID获取对应Token');
  console.log('');
  console.log('💡 优势:');
  console.log('   - 数据持久化，重启服务不丢失');
  console.log('   - 支持多工作区独立管理');
  console.log('   - 智能Token选择，提高成功率');
  console.log('   - 完整的用户-工作区映射关系');
}

// 运行测试
testSQLiteIntegration();
