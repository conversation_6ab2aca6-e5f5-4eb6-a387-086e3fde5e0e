// 单元测试：向指定 Slack 用户发送消息
require('dotenv').config();
const axios = require('axios');

// 导入应用中的消息发送函数（模拟）
async function openDirectMessage(userId) {
  try {
    console.log(`🔗 打开与用户 ${userId} 的私聊通道...`);
    
    const response = await axios.post('https://slack.com/api/conversations.open', {
      users: userId
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.ok) {
      const channelId = response.data.channel.id;
      console.log(`✅ 私聊通道打开成功: ${channelId}`);
      return channelId;
    } else {
      console.error('❌ 打开私聊通道失败:', response.data.error);
      throw new Error(`无法打开私聊通道: ${response.data.error}`);
    }
  } catch (error) {
    console.error('❌ 打开私聊通道异常:', error.message);
    throw error;
  }
}

async function sendSlackMessage(channel, text, userId = null) {
  try {
    console.log(`📤 发送Slack消息到频道 ${channel}:`);
    console.log(`   ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);

    let targetChannel = channel;

    // 如果是私信且提供了用户ID，先尝试打开私聊通道
    if (userId && (channel.startsWith('D') || channel === userId)) {
      try {
        targetChannel = await openDirectMessage(userId);
        console.log(`🔄 使用新的私聊通道ID: ${targetChannel}`);
      } catch (dmError) {
        console.log(`⚠️ 无法打开新的私聊通道，使用原始频道ID: ${channel}`);
        // 继续使用原始频道ID
      }
    }

    // 使用 Slack Web API 发送消息
    const response = await axios.post('https://slack.com/api/chat.postMessage', {
      channel: targetChannel,
      text: text
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.ok) {
      console.log(`✅ Slack消息发送成功: ${response.data.ts}`);
      return response.data;
    } else {
      console.error('❌ Slack API 详细错误:', response.data);
      throw new Error(`Slack API错误: ${response.data.error}`);
    }

  } catch (error) {
    console.error('❌ 发送Slack消息失败:', error.message);
    throw error;
  }
}

// 单元测试函数
async function testSendMessageToUser() {
  console.log('🧪 单元测试：向 Slack 用户发送消息');
  console.log('=' .repeat(60));

  // 测试目标用户
  const targetUserId = 'U0983BEK1HQ';
  const testMessage = '🧪 这是一条测试消息！\n\n您好，我是畅游网络的智能客服。这条消息用于测试私聊功能是否正常工作。\n\n如果您收到这条消息，说明我们的 Bot 已经可以正常发送私信了！';

  console.log('📋 测试信息:');
  console.log(`   目标用户ID: ${targetUserId}`);
  console.log(`   消息内容: ${testMessage.substring(0, 50)}...`);
  console.log(`   Bot Token: ${process.env.SLACK_BOT_TOKEN ? '已配置' : '未配置'}`);
  console.log('');

  // 检查环境变量
  if (!process.env.SLACK_BOT_TOKEN) {
    console.log('❌ 测试失败: SLACK_BOT_TOKEN 未配置');
    return false;
  }

  try {
    // 测试步骤 1: 验证 Bot 身份
    console.log('🔐 步骤 1: 验证 Bot 身份...');
    const authResponse = await axios.post('https://slack.com/api/auth.test', {}, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (!authResponse.data.ok) {
      console.log('❌ Bot 身份验证失败:', authResponse.data.error);
      return false;
    }

    console.log('✅ Bot 身份验证成功');
    console.log(`   Bot ID: ${authResponse.data.bot_id}`);
    console.log(`   团队: ${authResponse.data.team}`);
    console.log('');

    // 测试步骤 2: 打开私聊通道
    console.log('🔗 步骤 2: 打开私聊通道...');
    const channelId = await openDirectMessage(targetUserId);
    console.log('');

    // 测试步骤 3: 发送测试消息
    console.log('💬 步骤 3: 发送测试消息...');
    const messageResult = await sendSlackMessage(channelId, testMessage, targetUserId);
    console.log('');

    // 测试步骤 4: 验证发送结果
    console.log('✅ 步骤 4: 验证发送结果...');
    console.log(`   消息时间戳: ${messageResult.ts}`);
    console.log(`   频道ID: ${messageResult.channel}`);
    console.log(`   消息内容已发送到用户 ${targetUserId}`);
    console.log('');

    // 测试步骤 5: 发送确认消息
    console.log('📨 步骤 5: 发送确认消息...');
    const confirmMessage = '✅ 单元测试完成！如果您看到这条消息，说明 Slack 私聊功能工作正常。';
    await sendSlackMessage(channelId, confirmMessage, targetUserId);

    console.log('🎉 单元测试成功完成！');
    console.log('');
    console.log('📊 测试结果总结:');
    console.log('   ✅ Bot 身份验证通过');
    console.log('   ✅ 私聊通道打开成功');
    console.log('   ✅ 消息发送成功');
    console.log('   ✅ 确认消息发送成功');
    console.log('');
    console.log(`💬 请检查用户 ${targetUserId} 是否收到了测试消息`);

    return true;

  } catch (error) {
    console.error('❌ 单元测试失败:', error.message);
    
    // 详细错误分析
    if (error.message.includes('missing_scope')) {
      console.log('');
      console.log('🔧 权限问题解决方案:');
      console.log('1. 在 Slack App 设置中添加以下权限:');
      console.log('   - im:write (发送私信)');
      console.log('   - chat:write (发送消息)');
      console.log('   - users:read (读取用户信息)');
      console.log('2. 重新安装 App 到工作区');
      console.log('3. 更新 Bot Token');
    } else if (error.message.includes('channel_not_found')) {
      console.log('');
      console.log('🔧 用户访问问题解决方案:');
      console.log('1. 确认用户ID是否正确');
      console.log('2. 确保用户在同一个工作区');
      console.log('3. 检查用户是否允许接收 Bot 消息');
    } else if (error.message.includes('invalid_auth')) {
      console.log('');
      console.log('🔧 认证问题解决方案:');
      console.log('1. 检查 SLACK_BOT_TOKEN 是否正确');
      console.log('2. 确认 Token 是否已过期');
      console.log('3. 重新生成 Bot Token');
    }

    return false;
  }
}

// 运行单元测试
async function runTest() {
  console.log('🚀 开始运行单元测试...');
  console.log('');
  
  const success = await testSendMessageToUser();
  
  console.log('');
  console.log('📋 测试完成报告:');
  console.log(`   状态: ${success ? '✅ 成功' : '❌ 失败'}`);
  console.log(`   时间: ${new Date().toLocaleString()}`);
  
  if (success) {
    console.log('   结果: 消息已成功发送到指定用户');
  } else {
    console.log('   结果: 测试失败，请检查错误信息并修复');
  }
}

// 执行测试
runTest();
