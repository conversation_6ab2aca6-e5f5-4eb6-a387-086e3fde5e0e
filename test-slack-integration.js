// Slack 集成测试脚本
require('dotenv').config();
const axios = require('axios');

async function testSlackIntegration() {
  console.log('🧪 测试 Slack 集成...');
  console.log('=' .repeat(50));

  // 检查环境变量
  console.log('🔧 检查环境变量:');
  console.log(`   SLACK_BOT_TOKEN: ${process.env.SLACK_BOT_TOKEN ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`   SLACK_SIGNING_SECRET: ${process.env.SLACK_SIGNING_SECRET ? '✅ 已配置' : '❌ 未配置'}`);
  console.log('');

  if (!process.env.SLACK_BOT_TOKEN) {
    console.log('❌ 请在 .env 文件中配置 SLACK_BOT_TOKEN');
    return;
  }

  try {
    // 测试 Slack API 连接
    console.log('🔗 测试 Slack API 连接...');
    const authResponse = await axios.post('https://slack.com/api/auth.test', {}, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (authResponse.data.ok) {
      console.log('✅ Slack API 连接成功!');
      console.log(`   团队: ${authResponse.data.team}`);
      console.log(`   用户: ${authResponse.data.user}`);
      console.log(`   Bot ID: ${authResponse.data.bot_id}`);
    } else {
      console.log('❌ Slack API 连接失败:', authResponse.data.error);
      return;
    }

    // 测试本地服务器
    console.log('');
    console.log('🌐 测试本地服务器...');
    const serverPort = process.env.PORT || 3005;
    
    try {
      const healthResponse = await axios.get(`http://localhost:${serverPort}/health`);
      console.log('✅ 本地服务器运行正常');
      console.log(`   服务: ${healthResponse.data.service}`);
      console.log(`   Slack状态: ${healthResponse.data.slack?.success ? '✅ 已连接' : '❌ 未连接'}`);
    } catch (serverError) {
      console.log('❌ 本地服务器未运行，请先启动服务器:');
      console.log('   npm start');
      return;
    }

    // 模拟 Slack 事件
    console.log('');
    console.log('📨 模拟 Slack 事件...');
    
    const mockSlackEvent = {
      type: 'event_callback',
      event: {
        type: 'message',
        user: 'U**********',
        text: '你好，我想了解一下随身路由器',
        channel: 'C**********',
        ts: '**********.123456'
      }
    };

    try {
      const eventResponse = await axios.post(`http://localhost:${serverPort}/slack/events`, mockSlackEvent, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (eventResponse.status === 200) {
        console.log('✅ Slack 事件处理成功');
      } else {
        console.log('❌ Slack 事件处理失败:', eventResponse.status);
      }
    } catch (eventError) {
      console.log('❌ Slack 事件处理错误:', eventError.message);
    }

    console.log('');
    console.log('🎉 Slack 集成测试完成!');
    console.log('');
    console.log('📋 下一步配置指南:');
    console.log('1. 在 Slack App 设置中启用 Event Subscriptions');
    console.log('2. 设置 Request URL: https://your-ngrok-url.ngrok.io/slack/events');
    console.log('3. 订阅以下事件: message.channels, message.groups, message.im');
    console.log('4. 安装 App 到你的 Slack 工作区');
    console.log('5. 邀请 Bot 到需要的频道');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testSlackIntegration();
