// 测试推荐逻辑
require('dotenv').config();
const MessageProcessingService = require('./src/services/messageProcessingService');

async function testRecommendation() {
  console.log('🧪 测试推荐逻辑...');
  
  const messageProcessor = new MessageProcessingService();
  
  // 测试消息
  const testMessage = '我需要下个月去美国出差，我们一行 4 个人，为期一个月，你有什么推荐么';

  console.log('🔍 测试目标：验证复杂业务咨询的处理能力（时间+地点+人数+时长）');
  const from = 'whatsapp:+1234567890';
  const userName = 'TestUser';
  
  try {
    console.log(`📝 测试消息: "${testMessage}"`);
    
    const result = await messageProcessor.processMessage(from, testMessage, userName);
    
    console.log('\n📊 处理结果:');
    console.log(`成功: ${result.success}`);
    console.log(`意图: ${result.intent}`);
    console.log(`置信度: ${result.confidence}`);
    console.log(`使用知识: ${result.usedKnowledge}`);
    console.log('\n💬 AI回复:');
    console.log(result.response);

    console.log('\n🔍 格式检查:');
    const hasMarkdown = /(\*\*|__|##|###|\*\*\*|---|```)/g.test(result.response);
    console.log(`包含Markdown语法: ${hasMarkdown ? '❌ 是' : '✅ 否'}`);

    const hasEmoji = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu.test(result.response);
    console.log(`包含表情符号: ${hasEmoji ? '✅ 是' : '⚠️ 否'}`);

    console.log(`回复长度: ${result.response.length} 字符`);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testRecommendation();
