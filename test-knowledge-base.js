// 引入 dotenv 并立即配置，确保环境变量在所有代码执行前加载
require('dotenv').config();

const ZepKnowledgeManager = require('./zep-knowledge-manager');
const { ZepClient } = require('@getzep/zep-cloud');
const OpenAI = require('openai');

/**
 * 知识库功能快速测试
 */
async function testKnowledgeBase() {
  console.log('🧪 开始知识库功能测试 (新版API)...\n');

  // 检查环境变量
  if (!process.env.ZEP_API_KEY || !process.env.ARK_API_KEY) {
    console.error('❌ 缺少 ZEP_API_KEY 或 ARK_API_KEY 环境变量');
    return;
  }

  const manager = new ZepKnowledgeManager();
  const zepClient = new ZepClient({ apiKey: process.env.ZEP_API_KEY });
  const openaiClient = new OpenAI({
    apiKey: process.env.ARK_API_KEY,
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  });

  const testCollectionName = `test-kb-${Date.now()}`;
  const testUserId = `test-user-${Date.now()}`;
  const testSessionId = `test-session-${Date.now()}`;

  try {
    // 1. 创建知识库集合 (在新版中，这一步是隐式的，通过获取对象完成)
    console.log('📁 步骤1: 准备知识库集合...');
    await manager.getOrCreateCollection(testCollectionName);
    console.log('✅ 集合已准备就绪');

    // 2. 导入预定义知识
    console.log('\n📚 步骤2: 导入预定义知识...');
    await manager.importPredefinedKnowledge(testCollectionName);

    // 3. 创建用户和会话 (这部分逻辑不变)
    console.log('\n👤 步骤3: 创建测试用户和会话...');
    await zepClient.user.add({
      userId: testUserId,
      email: '<EMAIL>',
      firstName: '测试',
      lastName: '用户'
    });

    await zepClient.memory.addSession({
      sessionId: testSessionId,
      userId: testUserId,
      metadata: { type: 'knowledge-test' }
    });
    
    // 4. 测试知识库搜索
    console.log('\n🔍 步骤4: 测试知识库搜索...');
    const testQueries = ['公司主要业务是什么？', '技术支持联系方式'];

    for (const query of testQueries) {
      console.log(`\n查询: "${query}"`);
      const results = await manager.searchKnowledge(testCollectionName, query, 2);
      if (results.length > 0) {
        console.log(`✅ 找到 ${results.length} 个相关结果:`);
        results.forEach((r, i) => console.log(`   ${i+1}. [得分: ${r.score.toFixed(3)}] ${r.document.content}`));
      } else {
        console.log('⚠️ 未找到相关结果');
      }
    }

    // 5. 测试基于知识库的问答 (这部分逻辑不变)
    console.log('\n💬 步骤5: 测试基于知识库的智能问答...');
    async function knowledgeBasedQA(question) {
      console.log(`\n👤 问题: ${question}`);
      const searchResults = await manager.searchKnowledge(testCollectionName, question, 3);
      
      let knowledgeContext = '';
      if (searchResults.length > 0) {
        knowledgeContext = searchResults.map((result, index) => 
          `相关知识${index + 1} (相关度: ${result.score.toFixed(2)}):\n${result.document.content}`
        ).join('\n\n');
      }

      await zepClient.memory.add(testSessionId, { messages: [{ role: 'user', content: question }] });
      const memory = await zepClient.memory.get(testSessionId, { lastn: 5 });

      const systemPrompt = `你是一个专业的客服助手。请基于以下提供的“相关知识”来回答用户的问题。
---
相关知识:
${knowledgeContext || '未在知识库中找到相关内容。'}
---
对话历史:
${memory.messages.map(m => `${m.role}: ${m.content}`).join('\n')}
---
请根据“相关知识”提供准确、有帮助的回答。如果知识不足以回答，请诚实说明。`;

      const completion = await openaiClient.chat.completions.create({
        model: process.env.VOLCENGINE_MODEL_ENDPOINT || 'doubao-pro-4k',
        messages: [{ role: 'system', content: systemPrompt }, { role: 'user', content: question }],
        temperature: 0.2
      });

      const answer = completion.choices[0].message.content;
      console.log(`🤖 回答: ${answer}`);
      await zepClient.memory.add(testSessionId, { messages: [{ role: 'assistant', content: answer }] });
    }

    await knowledgeBasedQA('你们公司主要做什么业务？');
    await knowledgeBasedQA('如何联系技术支持？');
    
    console.log('\n🎉 知识库功能测试完成！');

  } catch (error) {
    console.error('\n❌ 测试过程中出错:', error.message);
    if (error.body) console.error('API响应体:', error.body);

  } finally {
    // 7. 清理测试数据
    console.log('\n🧹 清理测试数据...');
    try {
      await manager.deleteCollection(testCollectionName);
      await zepClient.user.delete(testUserId);
      console.log('✅ 测试用户已删除');
      console.log('✅ 清理完成');
    } catch (error) {
      console.log(`⚠️ 清理时出错: ${error.message}`);
    }
  }
}

testKnowledgeBase().catch(console.error);