// 测试Token刷新逻辑
require('dotenv').config();
const TokenManager = require('./src/services/tokenManager');
const SlackAuthDatabase = require('./src/database/slackAuth');

async function testTokenRefresh() {
  console.log('🧪 测试Token刷新逻辑...');
  console.log('=' .repeat(50));

  const tokenManager = new TokenManager();
  const slackAuthDB = new SlackAuthDatabase();

  try {
    // 1. 获取所有工作区
    console.log('1️⃣ 获取所有工作区...');
    const workspaces = await slackAuthDB.getAllWorkspaces();
    console.log(`找到 ${workspaces.length} 个工作区:`);
    
    workspaces.forEach((ws, index) => {
      console.log(`   ${index + 1}. ${ws.team_name} (${ws.team_id})`);
    });

    if (workspaces.length === 0) {
      console.log('❌ 没有找到工作区，无法测试');
      return;
    }

    // 2. 测试第一个工作区的Token获取
    const testWorkspace = workspaces[0];
    console.log(`\n2️⃣ 测试工作区 ${testWorkspace.team_name} 的Token获取...`);
    
    // 使用TokenManager获取Bot Token（智能刷新）
    console.log('   使用TokenManager.getBotToken()...');
    const smartToken = await tokenManager.getBotToken(testWorkspace.team_id);
    
    if (smartToken) {
      console.log('   ✅ TokenManager获取成功，Token长度:', smartToken.length);
      
      // 3. 测试Token有效性
      console.log('\n3️⃣ 测试Token有效性...');
      const axios = require('axios');
      
      const authResponse = await axios.post('https://slack.com/api/auth.test', {}, {
        headers: {
          'Authorization': `Bearer ${smartToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('   API响应:', {
        ok: authResponse.data.ok,
        error: authResponse.data.error,
        team: authResponse.data.team,
        user: authResponse.data.user
      });

      if (authResponse.data.ok) {
        console.log('   ✅ Token有效，可以正常使用');
      } else {
        console.log('   ❌ Token无效:', authResponse.data.error);
        
        if (authResponse.data.error === 'token_expired') {
          console.log('   🔄 Token已过期，这正是我们要修复的问题');
        }
      }
    } else {
      console.log('   ❌ TokenManager获取失败');
    }

    // 4. 对比旧方法
    console.log('\n4️⃣ 对比旧的直接数据库获取方法...');
    const directToken = await slackAuthDB.getBotTokenForTeam(testWorkspace.team_id);
    
    if (directToken) {
      console.log('   直接数据库获取Token长度:', directToken.length);
      console.log('   Token是否相同:', smartToken === directToken ? '✅' : '❌');
    } else {
      console.log('   ❌ 直接数据库获取失败');
    }

    // 5. 检查数据库中的过期时间
    console.log('\n5️⃣ 检查数据库中的Token过期信息...');
    const workspaceDetail = await slackAuthDB.getWorkspaceByTeamId(testWorkspace.team_id);
    
    if (workspaceDetail) {
      console.log('   工作区详细信息:');
      console.log('   - bot_user_access_token:', workspaceDetail.bot_user_access_token ? '存在' : '不存在');
      console.log('   - bot_refresh_token:', workspaceDetail.bot_refresh_token ? '存在' : '不存在');
      console.log('   - bot_token_expires_at:', workspaceDetail.bot_token_expires_at || '未设置');
      
      if (workspaceDetail.bot_token_expires_at) {
        const expiresAt = new Date(workspaceDetail.bot_token_expires_at);
        const now = new Date();
        const isExpired = expiresAt <= now;
        
        console.log('   - 过期时间:', expiresAt.toISOString());
        console.log('   - 当前时间:', now.toISOString());
        console.log('   - 是否过期:', isExpired ? '✅ 是' : '❌ 否');
      }
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
testTokenRefresh().then(() => {
  console.log('\n🏁 测试完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
