// 测试偏好提取功能
require('dotenv').config();
const MessageProcessingService = require('./src/services/messageProcessingService');

async function testPreferenceExtraction() {
  console.log('🧪 测试偏好提取功能...');
  
  const messageProcessor = new MessageProcessingService();
  
  // 测试消息
  const testMessages = [
    '我经常需要出差去美国，一般都是商务差旅，通常3-4个人一起',
    '我比较关注性价比，不需要太贵的产品',
    '续航时间很重要，因为经常开会用不了充电',
    '现在我又要去欧洲出差了，这次是5个人，有什么推荐吗？'
  ];
  
  const session = {
    userId: 'test-user',
    name: 'TestUser'
  };
  
  for (const message of testMessages) {
    console.log(`\n${'='.repeat(50)}`);
    console.log(`📝 测试消息: "${message}"`);
    
    try {
      const preferences = await messageProcessor.extractUserPreferences(message, session);
      console.log('🧠 提取的偏好:', JSON.stringify(preferences, null, 2));
      
      if (Object.keys(preferences).length === 0) {
        console.log('⚠️ 没有提取到任何偏好信息');
      } else {
        console.log('✅ 成功提取到偏好信息');
      }
      
    } catch (error) {
      console.error('❌ 偏好提取失败:', error.message);
    }
    
    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

testPreferenceExtraction();
