/**
 * Token 管理服务
 * 负责管理 Slack OAuth Token 的存储、刷新和过期处理
 */
const axios = require('axios');
const SlackAuthDatabase = require('../database/slackAuth');

class TokenManager {
  constructor() {
    this.db = new SlackAuthDatabase();
    this.tokenCache = new Map(); // 内存缓存: userId -> {token, expiresAt}
    this.refreshingTokens = new Map(); // 正在刷新的token: userId -> Promise
  }

  /**
   * 保存完整的OAuth令牌信息
   * @param {Object} oauthData - OAuth响应数据
   */
  async saveOAuthData(oauthData) {
    try {
      console.log('💾 保存OAuth令牌信息...');
      
      // 保存Bot令牌信息
      await this.db.saveWorkspaceAuth({
        team_id: oauthData.team.id,
        team_name: oauthData.team.name,
        bot_user_id: oauthData.bot_user_id,
        bot_user_access_token: oauthData.access_token,
        bot_refresh_token: oauthData.refresh_token,
        bot_token_expires_at: Date.now() + (oauthData.expires_in * 1000),
        authed_user_id: oauthData.authed_user.id,
        scope: oauthData.scope,
        installed_at: new Date().toISOString()
      });

      // 保存用户令牌信息
      await this.db.saveUserToken({
        user_id: oauthData.authed_user.id,
        team_id: oauthData.team.id,
        access_token: oauthData.authed_user.access_token,
        refresh_token: oauthData.authed_user.refresh_token,
        token_expires_at: Date.now() + (oauthData.authed_user.expires_in * 1000),
        scope: oauthData.authed_user.scope
      });

      // 保存用户映射
      await this.db.saveUserMapping(
        oauthData.authed_user.id,
        oauthData.team.id
      );

      console.log('✅ OAuth令牌信息保存成功');
      console.log('   团队:', oauthData.team.name);
      console.log('   用户ID:', oauthData.authed_user.id);
      console.log('   Bot ID:', oauthData.bot_user_id);
      
      return true;
    } catch (error) {
      console.error('❌ 保存OAuth令牌信息失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取用户的访问令牌
   * @param {string} userId - Slack用户ID
   * @returns {Promise<string>} - 访问令牌
   */
  async getUserToken(userId) {
    try {
      console.log(`🔑 获取用户 ${userId} 的访问令牌...`);
      
      // 1. 检查内存缓存
      if (this.tokenCache.has(userId)) {
        const cachedData = this.tokenCache.get(userId);
        
        // 检查是否过期
        if (cachedData.expiresAt > Date.now()) {
          console.log('✅ 使用缓存的用户令牌');
          return cachedData.token;
        } else {
          console.log('⚠️ 缓存的用户令牌已过期');
          this.tokenCache.delete(userId);
        }
      }

      // 2. 从数据库获取
      const tokenData = await this.db.getUserToken(userId);
      
      if (!tokenData) {
        console.log('⚠️ 数据库中未找到用户令牌');
        return null;
      }

      // 3. 检查是否过期
      if (tokenData.token_expires_at > Date.now()) {
        // 缓存并返回有效令牌
        this.tokenCache.set(userId, {
          token: tokenData.access_token,
          expiresAt: tokenData.token_expires_at
        });
        
        console.log('✅ 使用数据库中的用户令牌');
        return tokenData.access_token;
      }

      // 4. 令牌已过期，尝试刷新
      console.log('⚠️ 用户令牌已过期，尝试刷新...');
      
      // 检查是否已经有刷新操作在进行中
      if (this.refreshingTokens.has(userId)) {
        console.log('⏳ 等待正在进行的令牌刷新...');
        return this.refreshingTokens.get(userId);
      }

      // 创建刷新令牌的Promise
      const refreshPromise = this.refreshUserToken(userId, tokenData.refresh_token, tokenData.team_id);
      this.refreshingTokens.set(userId, refreshPromise);

      try {
        const newToken = await refreshPromise;
        return newToken;
      } finally {
        // 无论成功失败，都移除正在刷新的标记
        this.refreshingTokens.delete(userId);
      }
    } catch (error) {
      console.error(`❌ 获取用户 ${userId} 令牌失败:`, error.message);
      return null;
    }
  }

  /**
   * 刷新用户访问令牌
   * @param {string} userId - Slack用户ID
   * @param {string} refreshToken - 刷新令牌
   * @param {string} teamId - 团队ID
   * @returns {Promise<string>} - 新的访问令牌
   */
  async refreshUserToken(userId, refreshToken, teamId) {
    try {
      console.log(`🔄 刷新用户 ${userId} 的访问令牌...`);
      
      const response = await axios.post('https://slack.com/api/oauth.v2.access', {
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: refreshToken
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.ok) {
        throw new Error(`刷新令牌失败: ${response.data.error}`);
      }

      const newTokenData = response.data;
      
      // 更新数据库中的令牌信息
      await this.db.saveUserToken({
        user_id: userId,
        team_id: teamId,
        access_token: newTokenData.authed_user.access_token,
        refresh_token: newTokenData.authed_user.refresh_token,
        token_expires_at: Date.now() + (newTokenData.authed_user.expires_in * 1000),
        scope: newTokenData.authed_user.scope
      });

      // 更新缓存
      this.tokenCache.set(userId, {
        token: newTokenData.authed_user.access_token,
        expiresAt: Date.now() + (newTokenData.authed_user.expires_in * 1000)
      });

      console.log('✅ 用户令牌刷新成功');
      return newTokenData.authed_user.access_token;
    } catch (error) {
      console.error(`❌ 刷新用户 ${userId} 令牌失败:`, error.message);
      throw error;
    }
  }

  /**
   * 获取Bot访问令牌
   * @param {string} teamId - 团队ID
   * @returns {Promise<string>} - Bot访问令牌
   */
  async getBotToken(teamId) {
    try {
      // 从数据库获取工作区信息
      const workspace = await this.db.getWorkspaceByTeamId(teamId);
      
      if (!workspace) {
        console.log(`⚠️ 未找到团队 ${teamId} 的工作区信息`);
        return null;
      }

      // 检查令牌是否过期
      if (workspace.bot_token_expires_at && workspace.bot_token_expires_at > Date.now()) {
        return workspace.bot_user_access_token;
      }

      // 令牌已过期，尝试刷新
      if (workspace.bot_refresh_token) {
        console.log(`⚠️ 团队 ${teamId} 的Bot令牌已过期，尝试刷新...`);
        return this.refreshBotToken(teamId, workspace.bot_refresh_token);
      }

      return workspace.bot_user_access_token;
    } catch (error) {
      console.error(`❌ 获取团队 ${teamId} 的Bot令牌失败:`, error.message);
      return null;
    }
  }

  /**
   * 刷新Bot访问令牌
   * @param {string} teamId - 团队ID
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise<string>} - 新的Bot访问令牌
   */
  async refreshBotToken(teamId, refreshToken) {
    try {
      console.log(`🔄 刷新团队 ${teamId} 的Bot令牌...`);
      
      const response = await axios.post('https://slack.com/api/oauth.v2.access', {
        client_id: process.env.SLACK_CLIENT_ID,
        client_secret: process.env.SLACK_CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: refreshToken
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.ok) {
        throw new Error(`刷新Bot令牌失败: ${response.data.error}`);
      }

      const newTokenData = response.data;
      
      // 更新数据库中的令牌信息
      await this.db.updateWorkspaceToken(teamId, {
        bot_user_access_token: newTokenData.access_token,
        bot_refresh_token: newTokenData.refresh_token,
        bot_token_expires_at: Date.now() + (newTokenData.expires_in * 1000)
      });

      console.log('✅ Bot令牌刷新成功');
      return newTokenData.access_token;
    } catch (error) {
      console.error(`❌ 刷新团队 ${teamId} 的Bot令牌失败:`, error.message);
      throw error;
    }
  }

  /**
   * 清除用户令牌缓存
   * @param {string} userId - Slack用户ID
   */
  clearUserTokenCache(userId) {
    if (this.tokenCache.has(userId)) {
      this.tokenCache.delete(userId);
      console.log(`🧹 已清除用户 ${userId} 的令牌缓存`);
    }
  }
}

module.exports = TokenManager;
