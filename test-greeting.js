// 测试问候语处理和异常情况
require('dotenv').config();
const MessageProcessingService = require('./src/services/messageProcessingService');

async function testGreetingAndExceptions() {
  console.log('🧪 测试问候语处理和异常情况...');
  
  const messageProcessor = new MessageProcessingService();
  
  // 测试消息
  const testMessages = [
    '你好',
    'hello',
    '您好，我想了解一下',
    '', // 空消息测试
    null, // null 测试
  ];
  
  const from = 'whatsapp:+1234567890';
  const userName = 'TestUser';
  
  for (const testMessage of testMessages) {
    console.log(`\n${'='.repeat(50)}`);
    console.log(`📝 测试消息: "${testMessage}"`);
    
    try {
      if (testMessage === null || testMessage === '') {
        console.log('⚠️ 跳过无效消息测试');
        continue;
      }
      
      const result = await messageProcessor.processMessage(from, testMessage, userName);
      
      console.log('\n📊 处理结果:');
      console.log(`成功: ${result.success}`);
      console.log(`意图: ${result.intent}`);
      console.log(`置信度: ${result.confidence}`);
      console.log(`使用知识: ${result.usedKnowledge}`);
      console.log('\n💬 AI回复:');
      console.log(result.response);
      
      // 检查是否有异常
      if (result.response.includes('null') || result.response.includes('undefined')) {
        console.log('❌ 回复中包含异常值!');
      } else {
        console.log('✅ 回复正常');
      }
      
    } catch (error) {
      console.error('❌ 测试失败:', error.message);
      console.error('详细错误:', error.stack);
    }
    
    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

testGreetingAndExceptions();
