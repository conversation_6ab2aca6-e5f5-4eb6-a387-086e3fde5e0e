// 测试 Slack 消息处理
require('dotenv').config();
const axios = require('axios');

async function testSlackMessage() {
  console.log('🧪 测试 Slack 消息处理...');
  
  // 模拟 Slack 消息事件
  const messageEvent = {
    type: 'event_callback',
    event: {
      type: 'message',
      user: 'U1234567890',
      text: '你好，我想了解一下随身路由器的价格',
      channel: 'C1234567890',
      ts: '1234567890.123456'
    }
  };

  try {
    console.log('📨 发送测试消息事件...');
    console.log('消息内容:', messageEvent.event.text);
    
    const response = await axios.post('http://localhost:3005/slack/events', messageEvent, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    console.log('✅ 消息处理响应状态:', response.status);
    console.log('响应内容:', response.data);
    
    // 等待一下让服务器处理完成
    console.log('⏳ 等待服务器处理...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误内容:', error.response.data);
    }
  }
}

testSlackMessage();
