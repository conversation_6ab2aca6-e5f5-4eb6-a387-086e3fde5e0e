// 测试令牌一致性修复
require('dotenv').config();
const axios = require('axios');

async function testTokenConsistencyFix() {
  console.log('🔧 测试令牌一致性修复');
  console.log('=' .repeat(60));

  const serverPort = process.env.PORT || 3002;
  
  // 测试场景：模拟用户发送消息，验证令牌一致性
  console.log('1️⃣ 测试场景：用户发送消息');
  console.log('   目标：验证用户令牌打开通道后，使用相同令牌发送消息');
  console.log('');

  const mockSlackEvent = {
    type: 'event_callback',
    team_id: 'T0983BEJT4J',
    event: {
      type: 'message',
      user: 'U0983BEK1HQ',
      text: '测试令牌一致性修复 - 这条消息应该使用用户令牌完成整个流程',
      channel: 'D097W5SPBLM',  // 使用一个不同的频道ID来触发通道打开
      ts: Date.now() / 1000
    }
  };

  console.log('📨 发送模拟Slack事件:');
  console.log('   团队ID:', mockSlackEvent.team_id);
  console.log('   用户ID:', mockSlackEvent.event.user);
  console.log('   频道ID:', mockSlackEvent.event.channel);
  console.log('   消息内容:', mockSlackEvent.event.text);
  console.log('');

  try {
    console.log('🚀 发送事件到服务器...');
    console.log('   URL:', `http://localhost:${serverPort}/slack/events`);
    console.log('');
    console.log('🔍 观察要点:');
    console.log('   1. 是否使用用户令牌打开私聊通道');
    console.log('   2. 是否使用相同的用户令牌发送消息');
    console.log('   3. 是否避免了 channel_not_found 错误');
    console.log('   4. 整个流程的令牌一致性');
    console.log('');

    const eventResponse = await axios.post(`http://localhost:${serverPort}/slack/events`, mockSlackEvent, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    if (eventResponse.status === 200) {
      console.log('✅ 事件发送成功，状态码:', eventResponse.status);
      console.log('');
      console.log('📊 预期的日志输出应该包括:');
      console.log('   🔑 方案1: 尝试使用用户令牌打开私聊通道...');
      console.log('   ✅ 用户令牌方案成功');
      console.log('   🔑 Token选择完成: Token来源: 用户令牌（私聊通道）');
      console.log('   📤 使用Token类型: 用户令牌');
      console.log('   ✅ Slack消息发送成功');
      console.log('');
      console.log('❌ 不应该出现的错误:');
      console.log('   - channel_not_found 错误');
      console.log('   - Token类型不匹配');
      console.log('   - 权限不足错误');
    } else {
      console.log('❌ 事件发送失败，状态码:', eventResponse.status);
    }

  } catch (error) {
    console.log('❌ 发送事件失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('🔧 解决方案:');
      console.log('1. 启动服务器: npm start');
      console.log('2. 确认端口配置正确');
      console.log('3. 检查防火墙设置');
      return;
    }
  }

  // 等待一下让服务器处理完成
  console.log('⏳ 等待服务器处理完成...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  console.log('');
  console.log('📊 令牌一致性修复说明');
  console.log('=' .repeat(60));
  console.log('🔍 问题分析:');
  console.log('   原始问题: 用户令牌打开通道，但Bot令牌发送消息');
  console.log('   错误现象: channel_not_found（Bot无法访问用户令牌创建的通道）');
  console.log('   根本原因: Token类型不一致导致权限不匹配');
  console.log('');
  console.log('✅ 修复方案:');
  console.log('   1. 跟踪通道打开方式（用户令牌 vs Bot令牌）');
  console.log('   2. 使用相同类型的令牌发送消息');
  console.log('   3. 优先使用用户令牌完成整个流程');
  console.log('   4. 智能回退到Bot令牌（如果用户令牌失败）');
  console.log('');
  console.log('🔄 完整流程:');
  console.log('   步骤1: 检测私信需求');
  console.log('   步骤2: 尝试用户令牌打开通道');
  console.log('   步骤3: 记录使用的令牌类型');
  console.log('   步骤4: 使用相同令牌发送消息');
  console.log('   步骤5: 如果失败，回退到Bot令牌');
  console.log('');
  console.log('🎯 技术实现:');
  console.log('   - channelOpenedWithUserToken 标志');
  console.log('   - selectedToken 统一令牌管理');
  console.log('   - tokenSource 来源追踪');
  console.log('   - 详细的日志记录');
  console.log('');
  console.log('💡 优势:');
  console.log('   1. 令牌类型一致性保证');
  console.log('   2. 更高的消息发送成功率');
  console.log('   3. 智能回退机制');
  console.log('   4. 完整的错误处理');
  console.log('   5. 详细的调试信息');
  console.log('');
  console.log('🔒 安全考虑:');
  console.log('   - 用户令牌仅用于该用户的私聊');
  console.log('   - Bot令牌用于公共频道和回退');
  console.log('   - 令牌权限最小化原则');
  console.log('   - 完整的审计日志');
  console.log('');
  console.log('📈 性能优化:');
  console.log('   - 令牌缓存减少重复获取');
  console.log('   - 智能选择避免不必要的API调用');
  console.log('   - 并发控制防止重复刷新');
  console.log('   - 异步处理提高响应速度');
}

// 显示修复前后对比
function showBeforeAfterComparison() {
  console.log('');
  console.log('📋 修复前后对比');
  console.log('=' .repeat(60));
  
  console.log('❌ 修复前的问题流程:');
  console.log('   1. 用户令牌打开私聊通道 → 成功 (D0983BELLCA)');
  console.log('   2. Bot令牌发送消息 → 失败 (channel_not_found)');
  console.log('   3. 错误: Bot无法访问用户令牌创建的通道');
  console.log('');
  
  console.log('✅ 修复后的正确流程:');
  console.log('   1. 用户令牌打开私聊通道 → 成功 (D0983BELLCA)');
  console.log('   2. 用户令牌发送消息 → 成功 (令牌一致)');
  console.log('   3. 结果: 完整的用户令牌流程，无权限问题');
  console.log('');
  
  console.log('🔄 回退机制:');
  console.log('   如果用户令牌方案失败:');
  console.log('   1. Bot令牌打开私聊通道');
  console.log('   2. Bot令牌发送消息');
  console.log('   3. 保持令牌类型一致性');
  console.log('');
  
  console.log('🎯 关键改进:');
  console.log('   - selectedToken: 统一的令牌管理');
  console.log('   - channelOpenedWithUserToken: 状态跟踪');
  console.log('   - tokenSource: 来源标识');
  console.log('   - 详细的日志输出');
}

// 运行测试
async function runTest() {
  await testTokenConsistencyFix();
  showBeforeAfterComparison();
  
  console.log('');
  console.log('🎉 令牌一致性修复测试完成！');
  console.log('');
  console.log('📝 验证要点:');
  console.log('1. 检查服务器日志中的令牌类型一致性');
  console.log('2. 确认没有 channel_not_found 错误');
  console.log('3. 验证消息发送成功');
  console.log('4. 观察完整的用户令牌流程');
}

runTest();
