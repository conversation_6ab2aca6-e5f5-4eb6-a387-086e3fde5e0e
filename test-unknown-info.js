// 测试未知信息的处理
require('dotenv').config();
const MessageProcessingService = require('./src/services/messageProcessingService');

async function testUnknownInfoHandling() {
  console.log('🧪 测试未知信息的处理...');
  
  const messageProcessor = new MessageProcessingService();
  
  // 测试知识图谱中不存在的技术细节问题
  const testMessages = [
    {
      message: '那么充电器是支持美国的110V接口么？',
      description: '充电器电压兼容性问题'
    },
    {
      message: '设备支持WiFi 6吗？',
      description: 'WiFi技术规格问题'
    },
    {
      message: '可以同时连接多少个5G设备？',
      description: '具体技术参数问题'
    },
    {
      message: '设备的工作温度范围是多少？',
      description: '环境参数问题'
    },
    {
      message: '充电需要多长时间？',
      description: '充电时间问题'
    }
  ];
  
  const from = 'whatsapp:+1234567890';
  const userName = 'TestUser';
  
  for (const { message, description } of testMessages) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`📝 测试场景: ${description}`);
    console.log(`💬 用户消息: "${message}"`);
    
    try {
      const result = await messageProcessor.processMessage(from, message, userName);
      
      console.log('\n📊 处理结果:');
      console.log(`成功: ${result.success}`);
      console.log(`意图: ${result.intent}`);
      console.log(`使用知识: ${result.usedKnowledge}`);
      
      console.log('\n💬 AI回复:');
      console.log(result.response);
      
      // 检查回复是否正确处理了未知信息
      const response = result.response.toLowerCase();
      const hasUncertainty = 
        response.includes('不确定') || 
        response.includes('不清楚') || 
        response.includes('没有详细信息') ||
        response.includes('建议您查看') ||
        response.includes('联系技术支持') ||
        response.includes('查看说明书') ||
        response.includes('暂时没有');
      
      const hasDefinitiveAnswer =
        (response.includes('支持') && !response.includes('不确定') && !response.includes('暂时没有')) ||
        (response.includes('可以') && !response.includes('建议') && !response.includes('查看') && !response.includes('联系')) ||
        response.includes('是的') ||
        response.includes('当然') ||
        (response.includes('小时') && response.includes('充电') && !response.includes('暂时没有'));
      
      console.log(`\n🔍 回复分析:`);
      console.log(`包含不确定表述: ${hasUncertainty ? '✅ 是' : '❌ 否'}`);
      console.log(`包含肯定答案: ${hasDefinitiveAnswer ? '⚠️ 是' : '✅ 否'}`);
      
      if (hasUncertainty && !hasDefinitiveAnswer) {
        console.log(`✅ 正确处理：承认不确定并提供建议`);
      } else if (hasDefinitiveAnswer) {
        console.log(`❌ 问题：给出了肯定答案但可能缺乏依据`);
      } else {
        console.log(`⚠️ 需要检查：回复可能不够明确`);
      }
      
    } catch (error) {
      console.error('❌ 测试失败:', error.message);
    }
    
    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

testUnknownInfoHandling();
