# Slack Token 刷新逻辑修复总结

## 🔍 问题诊断

### 原始问题
- Slack回复消息失败，报错：`token_expired`
- 用户验证响应：`{"ok":false,"error":"token_expired","warning":"missing_charset","response_metadata":{"warnings":["missing_charset"]}}`

### 根本原因分析
1. **Token获取逻辑不一致**：
   - `src/app.js` 中的 `getBotTokenForUser()` 和 `getBotTokenForTeam()` 函数直接从数据库获取token，没有检查过期时间
   - `TokenManager` 类有完整的token过期检查和刷新逻辑，但没有被使用

2. **数据库字段缺失**：
   - `slack_workspaces` 表缺少 `bot_refresh_token` 和 `bot_token_expires_at` 字段
   - 无法进行token刷新

3. **OAuth数据保存错误**：
   - `TokenManager.saveOAuthData()` 方法错误地使用了 `oauthData.access_token` 作为 `bot_user_access_token`
   - 没有正确保存 bot 的 refresh token 和过期时间

## 🔧 修复方案

### 1. 更新数据库表结构

**文件**: `src/database/slackAuth.js`

```javascript
// 添加缺失字段到 slack_workspaces 表
CREATE TABLE IF NOT EXISTS slack_workspaces (
  team_id TEXT PRIMARY KEY,
  team_name TEXT,
  bot_user_id TEXT,
  bot_user_access_token TEXT,
  bot_refresh_token TEXT,           // 新增
  bot_token_expires_at INTEGER,     // 新增
  authed_user_id TEXT,
  scope TEXT,
  installed_at TEXT,
  updated_at TEXT,
  is_active INTEGER DEFAULT 1
)
```

**迁移逻辑**：
- 自动添加缺失字段（如果表已存在）
- 兼容现有数据

### 2. 修复OAuth数据保存逻辑

**文件**: `src/services/tokenManager.js`

**修复前**：
```javascript
bot_user_access_token: oauthData.access_token,  // 错误
bot_refresh_token: oauthData.refresh_token,     // 错误
```

**修复后**：
```javascript
bot_user_access_token: oauthData.access_token,  // 正确：这就是bot token
bot_refresh_token: oauthData.refresh_token,     // 正确：这是bot的refresh token
bot_token_expires_at: Date.now() + (oauthData.expires_in * 1000)
```

**关键改进**：
- 正确理解Slack OAuth v2 API响应格式
- 添加详细的调试日志
- 条件性保存（只有存在时才保存）

### 3. 升级Token获取逻辑

**文件**: `src/app.js`

**修复前**：
```javascript
async function getBotTokenForTeam(team_id) {
  // 直接从数据库获取，无过期检查
  const token = await slackAuthDB.getBotTokenForTeam(team_id);
  return token || process.env.SLACK_BOT_TOKEN;
}
```

**修复后**：
```javascript
async function getBotTokenForTeam(team_id) {
  try {
    // 使用TokenManager智能获取（自动处理过期和刷新）
    const token = await tokenManager.getBotToken(team_id);
    if (token) return token;
  } catch (error) {
    // 回退到数据库直接获取
    const fallbackToken = await slackAuthDB.getBotTokenForTeam(team_id);
    if (fallbackToken) return fallbackToken;
  }
  // 最终回退到全局Token
  return process.env.SLACK_BOT_TOKEN;
}
```

### 4. 添加用户映射查询功能

**文件**: `src/database/slackAuth.js`

```javascript
// 新增方法
async getUserMapping(user_id) {
  // 根据用户ID获取团队ID映射关系
  // 用于 getBotTokenForUser 函数
}
```

## 🧪 测试验证

### 测试脚本
- `test-token-refresh.js` - 基础token刷新测试
- `test-oauth-fix.js` - OAuth修复后的完整测试

### 测试流程
1. **检查数据库表结构** - 验证新字段是否存在
2. **检查现有token状态** - 验证过期时间和refresh token
3. **测试TokenManager智能获取** - 验证自动刷新逻辑
4. **测试API调用** - 验证token有效性
5. **测试用户验证** - 验证修复后的用户验证功能

## 📋 部署步骤

### 1. 重新启动服务
```bash
node src/app.js
```

### 2. 重新进行OAuth授权
访问：`http://localhost:3002/slack/oauth/start`

**重要**：必须重新授权以获取包含refresh token的新数据

### 3. 验证修复效果
```bash
node test-oauth-fix.js
```

## 🔄 Token刷新流程

### 修复后的完整流程
1. **请求Token** → `getBotTokenForUser/Team()`
2. **智能获取** → `tokenManager.getBotToken()`
3. **检查过期** → 比较 `bot_token_expires_at` 与当前时间
4. **自动刷新** → 如果过期且有 `bot_refresh_token`，调用 `refreshBotToken()`
5. **更新数据库** → 保存新的token和过期时间
6. **返回有效Token** → 用于API调用

### 回退机制
1. TokenManager智能获取
2. 数据库直接获取
3. 全局环境变量Token

## ✅ 预期效果

修复后应该解决：
- ❌ `token_expired` 错误
- ❌ 用户验证失败
- ❌ Slack消息发送失败

实现功能：
- ✅ 自动token刷新
- ✅ 智能过期检测
- ✅ 多层回退机制
- ✅ 详细错误日志

## 🚨 注意事项

1. **必须重新OAuth授权** - 现有token没有refresh token
2. **数据库自动迁移** - 新字段会自动添加
3. **向后兼容** - 保持对现有数据的兼容性
4. **监控日志** - 观察token刷新过程的日志输出
