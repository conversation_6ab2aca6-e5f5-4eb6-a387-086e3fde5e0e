// 测试 Slack Bot 权限
require('dotenv').config();
const axios = require('axios');

async function testSlackAuth() {
  console.log('🔐 测试 Slack Bot 权限');
  console.log('=' .repeat(50));

  // 检查环境变量
  if (!process.env.SLACK_BOT_TOKEN) {
    console.log('❌ SLACK_BOT_TOKEN 未配置');
    return;
  }

  try {
    const requestHeaders = {
      'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
      'Content-Type': 'application/json'
    };

    console.log('🔗 Slack API Request - auth.test:');
    console.log('   URL:', 'https://slack.com/api/auth.test');
    console.log('   Method:', 'POST');
    console.log('   Data:', '{}');
    console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });
    console.log('');

    const response = await axios.post('https://slack.com/api/auth.test', {}, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - auth.test:');
    console.log('   Status:', response.status);
    console.log('   Data:', JSON.stringify(response.data, null, 2));
    console.log('');

    if (response.data.ok) {
      console.log('✅ Bot 身份验证成功！');
      console.log('');
      console.log('📋 Bot 信息:');
      console.log(`   Bot ID: ${response.data.bot_id}`);
      console.log(`   用户ID: ${response.data.user_id}`);
      console.log(`   用户名: ${response.data.user}`);
      console.log(`   团队ID: ${response.data.team_id}`);
      console.log(`   团队名: ${response.data.team}`);
      console.log(`   URL: ${response.data.url}`);
      
      // 检查是否有企业信息
      if (response.data.enterprise_id) {
        console.log(`   企业ID: ${response.data.enterprise_id}`);
      }

      console.log('');
      console.log('🎯 下一步测试建议:');
      console.log('1. 测试 conversations.open API (需要 im:write 权限)');
      console.log('2. 测试 chat.postMessage API (需要 chat:write 权限)');
      console.log('3. 测试 users.info API (需要 users:read 权限)');

    } else {
      console.log('❌ Bot 身份验证失败！');
      console.log('');
      console.log('🔧 错误信息:');
      console.log(`   错误代码: ${response.data.error}`);
      
      if (response.data.warning) {
        console.log(`   警告: ${response.data.warning}`);
      }

      // 根据错误类型提供解决方案
      switch (response.data.error) {
        case 'invalid_auth':
          console.log('');
          console.log('💡 解决方案:');
          console.log('1. 检查 SLACK_BOT_TOKEN 是否正确');
          console.log('2. 确认 Token 格式为 xoxb-开头');
          console.log('3. 验证 Token 是否已过期');
          console.log('4. 重新生成 Bot Token');
          break;
        case 'account_inactive':
          console.log('');
          console.log('💡 解决方案:');
          console.log('1. 检查 Slack 工作区是否处于活跃状态');
          console.log('2. 确认 App 是否已被删除或暂停');
          break;
        case 'token_revoked':
          console.log('');
          console.log('💡 解决方案:');
          console.log('1. Token 已被撤销，需要重新安装 App');
          console.log('2. 重新生成 Bot Token');
          break;
        default:
          console.log('');
          console.log('💡 通用解决方案:');
          console.log('1. 检查网络连接');
          console.log('2. 验证 Slack App 配置');
          console.log('3. 查看 Slack API 文档');
      }
    }

  } catch (error) {
    console.error('❌ 请求失败:', error.message);
    
    if (error.response) {
      console.log('');
      console.log('📥 错误响应:');
      console.log('   Status:', error.response.status);
      console.log('   Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('');
      console.log('🌐 网络错误:');
      console.log('   请检查网络连接');
      console.log('   确认可以访问 slack.com');
    } else {
      console.log('');
      console.log('⚙️ 配置错误:');
      console.log('   请检查代码配置');
    }
  }

  console.log('');
  console.log('📊 权限测试完成');
}

// 运行测试
testSlackAuth();
