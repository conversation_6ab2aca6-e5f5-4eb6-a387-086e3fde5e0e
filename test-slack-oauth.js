// Slack OAuth 流程测试脚本
require('dotenv').config();
const axios = require('axios');

async function testSlackOAuth() {
  console.log('🧪 测试 Slack OAuth 流程...');
  console.log('=' .repeat(50));

  // 检查环境变量
  console.log('🔧 检查OAuth环境变量:');
  console.log(`   SLACK_CLIENT_ID: ${process.env.SLACK_CLIENT_ID ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`   SLACK_CLIENT_SECRET: ${process.env.SLACK_CLIENT_SECRET ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`   SLACK_REDIRECT_URI: ${process.env.SLACK_REDIRECT_URI ? '✅ 已配置' : '❌ 未配置'}`);
  console.log('');

  const serverPort = process.env.PORT || 3002;

  try {
    // 测试 OAuth 启动端点
    console.log('🚀 测试 OAuth 启动端点...');
    const startResponse = await axios.get(`http://localhost:${serverPort}/slack/oauth/start`, {
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400; // 允许重定向状态码
      }
    });

    if (startResponse.status === 302) {
      console.log('✅ OAuth 启动端点正常 (重定向到 Slack)');
      console.log(`   重定向URL: ${startResponse.headers.location}`);
      
      // 解析重定向URL中的参数
      const url = new URL(startResponse.headers.location);
      console.log(`   Client ID: ${url.searchParams.get('client_id')}`);
      console.log(`   Scopes: ${url.searchParams.get('scope')}`);
      console.log(`   Redirect URI: ${url.searchParams.get('redirect_uri')}`);
      console.log(`   State: ${url.searchParams.get('state')}`);
    } else {
      console.log('❌ OAuth 启动端点异常:', startResponse.status);
      if (startResponse.data) {
        console.log('   错误信息:', startResponse.data);
      }
    }

  } catch (error) {
    if (error.response && error.response.status === 302) {
      console.log('✅ OAuth 启动端点正常 (重定向到 Slack)');
      console.log(`   重定向URL: ${error.response.headers.location}`);
    } else {
      console.log('❌ OAuth 启动端点测试失败:', error.message);
      if (error.response) {
        console.log('   状态码:', error.response.status);
        console.log('   错误信息:', error.response.data);
      }
    }
  }

  // 测试 OAuth 回调端点（模拟错误情况）
  console.log('');
  console.log('🔑 测试 OAuth 回调端点...');
  
  try {
    // 测试缺少授权码的情况
    const callbackResponse = await axios.get(`http://localhost:${serverPort}/slack/oauth/callback`, {
      validateStatus: function (status) {
        return status >= 200 && status < 500;
      }
    });

    if (callbackResponse.status === 400) {
      console.log('✅ OAuth 回调端点正常处理错误情况');
      console.log('   错误响应:', callbackResponse.data);
    } else {
      console.log('⚠️ OAuth 回调端点响应异常:', callbackResponse.status);
    }

  } catch (error) {
    console.log('❌ OAuth 回调端点测试失败:', error.message);
  }

  console.log('');
  console.log('📋 OAuth 配置指南:');
  console.log('1. 在 Slack App 设置中配置 OAuth & Permissions');
  console.log('2. 设置 Redirect URLs:');
  console.log(`   - http://localhost:${serverPort}/slack/oauth/callback`);
  console.log(`   - https://your-domain.com/slack/oauth/callback`);
  console.log('3. 配置所需的 Bot Token Scopes:');
  console.log('   - chat:write');
  console.log('   - channels:read');
  console.log('   - groups:read');
  console.log('   - im:read');
  console.log('   - users:read');
  console.log('4. 获取 Client ID 和 Client Secret 并配置到 .env 文件');
  console.log('');
  console.log('🌐 手动测试OAuth流程:');
  console.log(`   访问: http://localhost:${serverPort}/slack/oauth/start`);
  console.log('   这将重定向到 Slack 授权页面');
  console.log('');
  console.log('🎉 OAuth 流程测试完成!');
}

// 运行测试
testSlackOAuth();
