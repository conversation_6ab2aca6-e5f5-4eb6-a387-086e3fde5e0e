const OpenAI = require('openai');
require('dotenv').config();

async function testVolcengineConnection() {
  console.log('🔍 开始测试火山方舟连接...\n');

  // 检查必要的环境变量
  if (!process.env.ARK_API_KEY) {
    console.error('❌ 错误: 未找到ARK_API_KEY环境变量');
    console.log('请在.env文件中设置ARK_API_KEY');
    console.log('获取方式: https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey');
    process.exit(1);
  }

  console.log('✅ 找到ARK_API_KEY环境变量');
  console.log(`🔑 API密钥前缀: ${process.env.ARK_API_KEY.substring(0, 10)}...`);

  // 检查模型端点配置
  const modelEndpoint = process.env.VOLCENGINE_MODEL_ENDPOINT || 'ep-20241221105607-2w8zx';
  console.log(`🤖 使用模型端点: ${modelEndpoint}`);

  // 初始化火山方舟客户端
  const openaiClient = new OpenAI({
    apiKey: process.env.ARK_API_KEY,
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  });

  console.log('✅ 火山方舟客户端初始化成功\n');

  try {
    // 测试1: 基本文本生成
    console.log('📝 测试1: 基本文本生成...');
    const basicResponse = await openaiClient.chat.completions.create({
      model: modelEndpoint,
      messages: [
        { role: 'user', content: '你好，请简单介绍一下你自己。' }
      ],
      max_tokens: 200,
      temperature: 0.7
    });

    const basicReply = basicResponse.choices[0].message.content;
    console.log('✅ 基本文本生成成功');
    console.log(`🤖 AI回复: ${basicReply.substring(0, 100)}...\n`);

    // 测试2: 专业问答
    console.log('📚 测试2: 专业问答能力...');
    const professionalResponse = await openaiClient.chat.completions.create({
      model: modelEndpoint,
      messages: [
        { 
          role: 'system', 
          content: '你是一个专业的AI技术顾问，请用简洁专业的语言回答问题。' 
        },
        { 
          role: 'user', 
          content: '请解释一下什么是大语言模型，以及它的主要应用场景。' 
        }
      ],
      max_tokens: 300,
      temperature: 0.5
    });

    const professionalReply = professionalResponse.choices[0].message.content;
    console.log('✅ 专业问答测试成功');
    console.log(`🎓 专业回复: ${professionalReply.substring(0, 150)}...\n`);

    // 测试3: 创意生成
    console.log('🎨 测试3: 创意生成能力...');
    const creativeResponse = await openaiClient.chat.completions.create({
      model: modelEndpoint,
      messages: [
        { 
          role: 'user', 
          content: '请为一个AI聊天机器人项目起3个有创意的名字，并简单说明理由。' 
        }
      ],
      max_tokens: 250,
      temperature: 0.9
    });

    const creativeReply = creativeResponse.choices[0].message.content;
    console.log('✅ 创意生成测试成功');
    console.log(`🎭 创意回复: ${creativeReply.substring(0, 150)}...\n`);

    // 测试4: 多轮对话
    console.log('💬 测试4: 多轮对话能力...');
    const conversation = [
      { role: 'user', content: '我想学习编程，你有什么建议吗？' },
      { role: 'assistant', content: '学习编程是很好的选择！建议从Python开始，它语法简单易学。你有特定的应用方向吗？' },
      { role: 'user', content: '我对人工智能比较感兴趣。' }
    ];

    const conversationResponse = await openaiClient.chat.completions.create({
      model: modelEndpoint,
      messages: conversation,
      max_tokens: 300,
      temperature: 0.7
    });

    const conversationReply = conversationResponse.choices[0].message.content;
    console.log('✅ 多轮对话测试成功');
    console.log(`💭 对话回复: ${conversationReply.substring(0, 150)}...\n`);

    // 测试5: 参数调优效果
    console.log('⚙️ 测试5: 参数调优效果...');
    
    const testPrompt = '请用一句话总结人工智能的发展趋势。';
    
    // 低温度 (更确定性)
    const lowTempResponse = await openaiClient.chat.completions.create({
      model: modelEndpoint,
      messages: [{ role: 'user', content: testPrompt }],
      max_tokens: 100,
      temperature: 0.1
    });

    // 高温度 (更创造性)
    const highTempResponse = await openaiClient.chat.completions.create({
      model: modelEndpoint,
      messages: [{ role: 'user', content: testPrompt }],
      max_tokens: 100,
      temperature: 0.9
    });

    console.log('✅ 参数调优测试成功');
    console.log(`🔧 低温度回复 (确定性): ${lowTempResponse.choices[0].message.content}`);
    console.log(`🎲 高温度回复 (创造性): ${highTempResponse.choices[0].message.content}\n`);

    // 测试6: 图像分析 (如果支持)
    console.log('🖼️ 测试6: 多模态能力 (图像分析)...');
    try {
      const imageUrl = 'https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/640px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg';
      
      const imageResponse = await openaiClient.chat.completions.create({
        model: modelEndpoint,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'image_url',
                image_url: { url: imageUrl }
              },
              { type: 'text', text: '请描述这张图片中的内容。' }
            ]
          }
        ],
        max_tokens: 200
      });

      const imageAnalysis = imageResponse.choices[0].message.content;
      console.log('✅ 多模态图像分析成功');
      console.log(`👁️ 图像分析: ${imageAnalysis}\n`);
      
    } catch (error) {
      console.log('⚠️ 多模态功能可能不支持或需要特殊配置');
      console.log(`   错误信息: ${error.message}\n`);
    }

    // 性能统计
    console.log('📊 连接测试统计:');
    console.log('✅ 基本文本生成: 通过');
    console.log('✅ 专业问答: 通过');
    console.log('✅ 创意生成: 通过');
    console.log('✅ 多轮对话: 通过');
    console.log('✅ 参数调优: 通过');
    console.log('⚠️ 多模态功能: 需要验证');

    console.log('\n🎉 火山方舟连接测试完成！API工作正常。');
    console.log('\n💡 接下来您可以:');
    console.log('1. 运行 npm run test:volcengine-zep 进行集成测试');
    console.log('2. 运行 npm run demo:volcengine-zep 查看完整演示');
    console.log('3. 在您的应用中集成火山方舟和Zep功能');

  } catch (error) {
    console.error('\n❌ 火山方舟连接测试失败:', error.message);
    
    if (error.response) {
      console.error('HTTP状态码:', error.response.status);
      console.error('错误详情:', error.response.data);
    }

    if (error.message.includes('model')) {
      console.log('\n💡 可能的解决方案:');
      console.log('1. 检查VOLCENGINE_MODEL_ENDPOINT是否正确');
      console.log('2. 确认模型端点已在火山方舟控制台创建并启用');
      console.log('3. 验证API密钥是否有访问该端点的权限');
    }

    if (error.message.includes('auth') || error.message.includes('401')) {
      console.log('\n💡 认证问题解决方案:');
      console.log('1. 检查ARK_API_KEY是否正确');
      console.log('2. 确认API密钥未过期');
      console.log('3. 验证API密钥权限设置');
    }

    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testVolcengineConnection().catch(console.error);
}

module.exports = { testVolcengineConnection };
