// 测试具体的 Slack Bot 权限
require('dotenv').config();
const axios = require('axios');

async function testSpecificPermissions() {
  console.log('🔍 测试具体的 Slack Bot 权限');
  console.log('=' .repeat(60));

  const testUserId = 'U0983BEK1HQ'; // 目标测试用户
  const requestHeaders = {
    'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
    'Content-Type': 'application/json'
  };

  const results = {
    auth_test: false,
    conversations_open: false,
    chat_postMessage: false,
    users_info: false,
    conversations_list: false
  };

  // 1. 测试基础身份验证 (已知成功)
  console.log('1️⃣ 测试 auth.test (基础身份验证)');
  console.log('   ✅ 已验证成功 - Bot ID: B098JR4NGTA');
  results.auth_test = true;
  console.log('');

  // 2. 测试 conversations.open (需要 im:write 权限)
  console.log('2️⃣ 测试 conversations.open (需要 im:write 权限)');
  try {
    const openRequestData = { users: testUserId };
    
    console.log('🔗 Slack API Request - conversations.open:');
    console.log('   URL: https://slack.com/api/conversations.open');
    console.log('   Data:', JSON.stringify(openRequestData));
    console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });

    const openResponse = await axios.post('https://slack.com/api/conversations.open', openRequestData, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - conversations.open:');
    console.log('   Status:', openResponse.status);
    console.log('   Data:', JSON.stringify(openResponse.data, null, 2));

    if (openResponse.data.ok) {
      console.log('   ✅ conversations.open 权限正常');
      results.conversations_open = true;
    } else {
      console.log('   ❌ conversations.open 权限不足:', openResponse.data.error);
      if (openResponse.data.needed) {
        console.log('   💡 需要权限:', openResponse.data.needed);
      }
      if (openResponse.data.provided) {
        console.log('   📋 当前权限:', openResponse.data.provided);
      }
    }
  } catch (error) {
    console.log('   ❌ conversations.open 测试失败:', error.message);
  }
  console.log('');

  // 3. 测试 users.info (需要 users:read 权限)
  console.log('3️⃣ 测试 users.info (需要 users:read 权限)');
  try {
    const userRequestData = { user: testUserId };
    
    console.log('🔗 Slack API Request - users.info:');
    console.log('   URL: https://slack.com/api/users.info');
    console.log('   Data:', JSON.stringify(userRequestData));
    console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });

    const userResponse = await axios.post('https://slack.com/api/users.info', userRequestData, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - users.info:');
    console.log('   Status:', userResponse.status);
    console.log('   Data:', JSON.stringify(userResponse.data, null, 2));

    if (userResponse.data.ok) {
      console.log('   ✅ users.info 权限正常');
      results.users_info = true;
    } else {
      console.log('   ❌ users.info 权限不足:', userResponse.data.error);
      if (userResponse.data.needed) {
        console.log('   💡 需要权限:', userResponse.data.needed);
      }
      if (userResponse.data.provided) {
        console.log('   📋 当前权限:', userResponse.data.provided);
      }
    }
  } catch (error) {
    console.log('   ❌ users.info 测试失败:', error.message);
  }
  console.log('');

  // 4. 测试 conversations.list (需要 channels:read 权限)
  console.log('4️⃣ 测试 conversations.list (需要 channels:read 权限)');
  try {
    const listRequestData = { types: 'public_channel,private_channel,im', limit: 5 };
    
    console.log('🔗 Slack API Request - conversations.list:');
    console.log('   URL: https://slack.com/api/conversations.list');
    console.log('   Data:', JSON.stringify(listRequestData));
    console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });

    const listResponse = await axios.post('https://slack.com/api/conversations.list', listRequestData, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - conversations.list:');
    console.log('   Status:', listResponse.status);
    console.log('   Data:', JSON.stringify(listResponse.data, null, 2));

    if (listResponse.data.ok) {
      console.log('   ✅ conversations.list 权限正常');
      results.conversations_list = true;
    } else {
      console.log('   ❌ conversations.list 权限不足:', listResponse.data.error);
      if (listResponse.data.needed) {
        console.log('   💡 需要权限:', listResponse.data.needed);
      }
      if (listResponse.data.provided) {
        console.log('   📋 当前权限:', listResponse.data.provided);
      }
    }
  } catch (error) {
    console.log('   ❌ conversations.list 测试失败:', error.message);
  }
  console.log('');

  // 5. 如果 conversations.open 成功，测试 chat.postMessage
  if (results.conversations_open) {
    console.log('5️⃣ 测试 chat.postMessage (需要 chat:write 权限)');
    try {
      // 先获取私聊通道ID
      const openResponse = await axios.post('https://slack.com/api/conversations.open', { users: testUserId }, {
        headers: requestHeaders
      });
      
      if (openResponse.data.ok) {
        const channelId = openResponse.data.channel.id;
        const messageRequestData = {
          channel: channelId,
          text: '🧪 权限测试消息 - 这是一条自动化测试消息，用于验证 chat:write 权限是否正常工作。'
        };
        
        console.log('🔗 Slack API Request - chat.postMessage:');
        console.log('   URL: https://slack.com/api/chat.postMessage');
        console.log('   Data:', JSON.stringify(messageRequestData));
        console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });

        const messageResponse = await axios.post('https://slack.com/api/chat.postMessage', messageRequestData, {
          headers: requestHeaders
        });

        console.log('📥 Slack API Response - chat.postMessage:');
        console.log('   Status:', messageResponse.status);
        console.log('   Data:', JSON.stringify(messageResponse.data, null, 2));

        if (messageResponse.data.ok) {
          console.log('   ✅ chat.postMessage 权限正常');
          results.chat_postMessage = true;
        } else {
          console.log('   ❌ chat.postMessage 权限不足:', messageResponse.data.error);
        }
      }
    } catch (error) {
      console.log('   ❌ chat.postMessage 测试失败:', error.message);
    }
    console.log('');
  }

  // 总结报告
  console.log('📊 权限测试总结报告');
  console.log('=' .repeat(60));
  console.log('✅ 成功的权限:');
  Object.entries(results).forEach(([api, success]) => {
    if (success) {
      console.log(`   ✅ ${api}`);
    }
  });

  console.log('');
  console.log('❌ 失败的权限:');
  Object.entries(results).forEach(([api, success]) => {
    if (!success) {
      console.log(`   ❌ ${api}`);
    }
  });

  console.log('');
  console.log('🔧 需要添加的权限:');
  if (!results.conversations_open) {
    console.log('   - im:write (发送私信)');
  }
  if (!results.users_info) {
    console.log('   - users:read (读取用户信息)');
  }
  if (!results.conversations_list) {
    console.log('   - channels:read (读取频道信息)');
    console.log('   - groups:read (读取私有频道信息)');
    console.log('   - im:read (读取私信信息)');
  }

  console.log('');
  console.log('🎯 修复建议:');
  console.log('1. 访问 https://api.slack.com/apps');
  console.log('2. 选择您的 App');
  console.log('3. 进入 "OAuth & Permissions"');
  console.log('4. 在 "Bot Token Scopes" 中添加缺少的权限');
  console.log('5. 点击 "Reinstall App" 重新安装');
  console.log('6. 重新运行此测试验证权限');
}

// 运行测试
testSpecificPermissions();
