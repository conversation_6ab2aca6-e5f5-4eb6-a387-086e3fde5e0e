const { ZepClient } = require('@getzep/zep-cloud');
require('dotenv').config();

async function testZepConnection() {
  console.log('🔍 开始测试Zep Cloud连接...\n');

  // 检查API密钥是否存在
  if (!process.env.ZEP_API_KEY) {
    console.error('❌ 错误: 未找到ZEP_API_KEY环境变量');
    console.log('请确保在.env文件中设置了ZEP_API_KEY');
    process.exit(1);
  }

  console.log('✅ 找到ZEP_API_KEY环境变量');
  console.log(`🔑 API密钥前缀: ${process.env.ZEP_API_KEY.substring(0, 10)}...`);

  // 初始化Zep客户端
  const zepClient = new ZepClient({
    apiKey: process.env.ZEP_API_KEY
  });

  console.log('✅ Zep客户端初始化成功\n');

  // 生成测试用的唯一ID
  const timestamp = Date.now();
  const testUserId = `test-user-${timestamp}`;
  const testSessionId = `test-session-${timestamp}`;

  try {
    // 测试1: 创建用户
    console.log('📝 测试1: 创建测试用户...');
    const userData = {
      userId: testUserId,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      metadata: {
        source: 'connection-test',
        timestamp: new Date().toISOString()
      }
    };

    const user = await zepClient.user.add(userData);
    console.log('✅ 用户创建成功:', {
      userId: user.userId,
      email: user.email,
      name: `${user.firstName} ${user.lastName}`
    });

    // 测试2: 获取用户
    console.log('\n📖 测试2: 获取用户信息...');
    const retrievedUser = await zepClient.user.get(testUserId);
    console.log('✅ 用户获取成功:', {
      userId: retrievedUser.userId,
      email: retrievedUser.email
    });

    // 测试3: 创建会话
    console.log('\n💬 测试3: 创建聊天会话...');
    const sessionData = {
      sessionId: testSessionId,
      userId: testUserId,
      metadata: {
        source: 'connection-test',
        type: 'chat',
        timestamp: new Date().toISOString()
      }
    };

    const session = await zepClient.memory.addSession(sessionData);
    console.log('✅ 会话创建成功:', {
      sessionId: session.sessionId,
      userId: session.userId
    });

    // 测试4: 添加聊天消息
    console.log('\n💭 测试4: 添加聊天消息到记忆...');
    const messages = [
      {
        roleType: 'user',
        content: '你好，这是一个连接测试。我的名字是张三，我是一名软件工程师。'
      },
      {
        roleType: 'assistant',
        content: '你好张三！很高兴认识你。作为一名软件工程师，你一定有很多有趣的项目经验。有什么我可以帮助你的吗？'
      }
    ];

    const memoryResult = await zepClient.memory.add(testSessionId, {
      messages: messages,
      returnContext: true
    });

    console.log('✅ 消息添加成功');
    if (memoryResult.context) {
      console.log('📋 获得上下文信息:', memoryResult.context.substring(0, 100) + '...');
    }

    // 测试5: 获取记忆
    console.log('\n🧠 测试5: 获取会话记忆...');
    const memory = await zepClient.memory.get(testSessionId);
    console.log('✅ 记忆获取成功:', {
      消息数量: memory.messages?.length || 0,
      有摘要: !!memory.summary,
      有上下文: !!memory.context
    });

    // 测试6: 添加知识图谱数据
    console.log('\n🕸️ 测试6: 添加知识图谱数据...');
    const graphData = {
      name: '张三',
      occupation: '软件工程师',
      skills: ['JavaScript', 'Node.js', 'React', 'Python'],
      experience: '5年',
      location: '北京',
      interests: ['编程', '人工智能', '开源项目']
    };

    await zepClient.graph.add({
      userId: testUserId,
      type: 'json',
      data: JSON.stringify(graphData)
    });

    console.log('✅ 知识图谱数据添加成功');

    // 等待一段时间让数据处理
    console.log('\n⏳ 等待数据处理...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 测试7: 搜索知识图谱
    console.log('\n🔍 测试7: 搜索知识图谱...');
    try {
      const searchResults = await zepClient.graph.search({
        userId: testUserId,
        query: '用户有什么技能？'
      });

      console.log('✅ 知识图谱搜索成功');
      if (searchResults.edges && searchResults.edges.length > 0) {
        console.log('📊 搜索结果:');
        searchResults.edges.slice(0, 3).forEach((edge, index) => {
          console.log(`  ${index + 1}. ${edge.fact}`);
        });
      }
    } catch (error) {
      console.log('⚠️ 知识图谱搜索可能需要更多时间处理数据');
    }

    console.log('\n🎉 所有测试完成！Zep Cloud连接正常工作。');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    if (error.statusCode) {
      console.error('状态码:', error.statusCode);
    }
    if (error.body) {
      console.error('错误详情:', error.body);
    }
  } finally {
    // 清理测试数据
    console.log('\n🧹 清理测试数据...');
    try {
      await zepClient.memory.deleteSession(testSessionId);
      console.log('✅ 测试会话已删除');
    } catch (error) {
      console.log('⚠️ 清理会话时出错:', error.message);
    }

    try {
      await zepClient.user.delete(testUserId);
      console.log('✅ 测试用户已删除');
    } catch (error) {
      console.log('⚠️ 清理用户时出错:', error.message);
    }

    console.log('✅ 清理完成');
  }
}

// 运行测试
if (require.main === module) {
  testZepConnection().catch(console.error);
}

module.exports = { testZepConnection };
