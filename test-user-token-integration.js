// 测试用户令牌集成功能
require('dotenv').config();
const TokenManager = require('./src/services/tokenManager');
const axios = require('axios');

async function testUserTokenIntegration() {
  console.log('🔑 测试用户令牌集成功能');
  console.log('=' .repeat(60));

  const tokenManager = new TokenManager();

  try {
    // 1. 模拟保存OAuth数据（使用您提供的新令牌数据）
    console.log('1️⃣ 模拟保存OAuth数据...');
    
    const mockOAuthData = {
      ok: true,
      app_id: 'A098JQC5A2U',
      authed_user: {
        id: 'U0983BEK1HQ',
        scope: 'im:read,users:read,chat:write,im:write',
        access_token: 'xoxe.xoxp-1-Mi0yLTkyNzUzODg2MzkxNTQtOTI3NTM4ODY0NzYwMi05MjgyNjg4MDkwMjYwLTkyODI2ODgxMDAwNjgtMmU1NmRkOTQzOGE3MjRlNzIyNDIyOWI4NmM0Y2FhNDFmNmU3MzZjMTNiYzNiZTU4MmJiMTRkZWU5Y2Q2NzBiYw',
        token_type: 'user',
        refresh_token: 'xoxe-1-My0xLTkyNzUzODg2MzkxNTQtOTI4MjY4ODA5MDI2MC05MjgyNjg4MTg4MzI0LTQxZmUwYmRlMmQ3NmU0NDQ5M2ExNjk0OTVhMTI0MDllMmI0NzY1N2RlOWU3ZGRjMTFlNjY3NTg0YzFmNDJlYmU',
        expires_in: 43200
      },
      scope: 'channels:history,channels:read,chat:write,groups:history,groups:read,im:history,im:read,mpim:history,users:read,im:write,channels:manage,channels:write.invites,channels:write.topic,groups:write,mpim:write,im:write.topic',
      token_type: 'bot',
      access_token: 'xoxe.xoxb-1-MS0yLTkyNzUzODg2MzkxNTQtOTI1OTg1NDg2Mzg3OS05MjU5ODU0OTc5NDQ3LTkyNjYxNjkxNzAxODMtYWFkNTBkZTkyNmYzOTliYjc3MTNiNmFlMjFmOTY4OGExNWVjMTA2YmZmMDc4N2IwYWM2NGM4YWUzNmJjN2Y4YQ',
      bot_user_id: 'U097MR4RDRV',
      refresh_token: 'xoxe-1-My0xLTkyNzUzODg2MzkxNTQtOTI1OTg1NDk3OTQ0Ny05MjU5ODU0OTc5NTU5LWRlYmVkODAzYzA2ZDQ3NTVhMzgyN2VlN2JjNzllNDk1ZTZmMjA3ZmVmMmUyZWU2MGM5ZGE5OGM2MGVkMzIwMDQ',
      expires_in: 42556,
      team: { id: 'T0983BEJT4J', name: 'xw' },
      enterprise: null,
      is_enterprise_install: false
    };

    await tokenManager.saveOAuthData(mockOAuthData);
    console.log('✅ OAuth数据保存成功');

    // 2. 测试获取用户令牌
    console.log('\n2️⃣ 测试获取用户令牌...');
    
    const userId = 'U0983BEK1HQ';
    const userToken = await tokenManager.getUserToken(userId);
    
    if (userToken) {
      console.log('✅ 用户令牌获取成功');
      console.log('   用户ID:', userId);
      console.log('   令牌长度:', userToken.length);
      console.log('   令牌前缀:', userToken.substring(0, 20) + '...');
    } else {
      console.log('❌ 用户令牌获取失败');
      return;
    }

    // 3. 测试使用用户令牌调用 Slack API
    console.log('\n3️⃣ 测试使用用户令牌调用 Slack API...');
    
    // 3.1 测试 auth.test
    console.log('   3.1 测试 auth.test...');
    try {
      const authResponse = await axios.post('https://slack.com/api/auth.test', {}, {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('   📥 auth.test 响应:');
      console.log('     成功:', authResponse.data.ok);
      
      if (authResponse.data.ok) {
        console.log('     用户ID:', authResponse.data.user_id);
        console.log('     用户名:', authResponse.data.user);
        console.log('     团队ID:', authResponse.data.team_id);
        console.log('     团队名:', authResponse.data.team);
      } else {
        console.log('     错误:', authResponse.data.error);
      }
    } catch (authError) {
      console.log('   ❌ auth.test 失败:', authError.message);
    }

    // 3.2 测试 conversations.open
    console.log('\n   3.2 测试 conversations.open...');
    try {
      const conversationResponse = await axios.post('https://slack.com/api/conversations.open', {
        users: userId  // 尝试与自己打开私聊通道
      }, {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('   📥 conversations.open 响应:');
      console.log('     成功:', conversationResponse.data.ok);
      
      if (conversationResponse.data.ok) {
        console.log('     频道ID:', conversationResponse.data.channel.id);
        console.log('     ✅ 使用用户令牌打开私聊通道成功！');
        
        // 3.3 测试发送消息
        console.log('\n   3.3 测试发送消息...');
        const messageResponse = await axios.post('https://slack.com/api/chat.postMessage', {
          channel: conversationResponse.data.channel.id,
          text: '🧪 用户令牌测试消息 - 这条消息使用用户的访问令牌发送，验证用户令牌功能是否正常工作。'
        }, {
          headers: {
            'Authorization': `Bearer ${userToken}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('   📥 chat.postMessage 响应:');
        console.log('     成功:', messageResponse.data.ok);
        
        if (messageResponse.data.ok) {
          console.log('     消息时间戳:', messageResponse.data.ts);
          console.log('     ✅ 使用用户令牌发送消息成功！');
        } else {
          console.log('     错误:', messageResponse.data.error);
        }
      } else {
        console.log('     错误:', conversationResponse.data.error);
        if (conversationResponse.data.needed) {
          console.log('     需要权限:', conversationResponse.data.needed);
        }
      }
    } catch (conversationError) {
      console.log('   ❌ conversations.open 失败:', conversationError.message);
    }

    // 4. 测试服务器端集成
    console.log('\n4️⃣ 测试服务器端集成...');
    
    const serverPort = process.env.PORT || 3002;
    const mockSlackEvent = {
      type: 'event_callback',
      team_id: 'T0983BEJT4J',
      event: {
        type: 'message',
        user: 'U0983BEK1HQ',
        text: '测试用户令牌集成功能',
        channel: 'D097W5SPBLM',
        ts: Date.now() / 1000
      }
    };

    try {
      console.log('📨 发送模拟事件到服务器...');
      console.log('   用户ID:', mockSlackEvent.event.user);
      console.log('   团队ID:', mockSlackEvent.team_id);
      console.log('   消息:', mockSlackEvent.event.text);

      const eventResponse = await axios.post(`http://localhost:${serverPort}/slack/events`, mockSlackEvent, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      if (eventResponse.status === 200) {
        console.log('✅ 服务器事件处理成功');
        console.log('   状态码:', eventResponse.status);
        console.log('   服务器应该显示用户令牌优先的详细日志');
      } else {
        console.log('❌ 服务器事件处理失败:', eventResponse.status);
      }

    } catch (serverError) {
      console.log('❌ 服务器测试失败:', serverError.message);
      if (serverError.code === 'ECONNREFUSED') {
        console.log('💡 请确保服务器正在运行: npm start');
      }
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }

  console.log('\n📊 用户令牌集成功能总结');
  console.log('=' .repeat(60));
  console.log('✅ 实现的功能:');
  console.log('   1. OAuth数据完整保存（用户令牌+Bot令牌）');
  console.log('   2. 用户令牌自动获取和缓存');
  console.log('   3. 令牌过期检测和自动刷新');
  console.log('   4. 用户令牌优先的私聊通道打开');
  console.log('   5. Bot令牌回退机制');
  console.log('');
  console.log('🔑 令牌管理策略:');
  console.log('   - 优先使用用户令牌（更高权限）');
  console.log('   - 自动检测令牌过期');
  console.log('   - 使用refresh_token自动刷新');
  console.log('   - 内存缓存提高性能');
  console.log('   - Bot令牌作为回退方案');
  console.log('');
  console.log('🎯 使用场景:');
  console.log('   1. 用户主动发起私聊 → 使用用户令牌');
  console.log('   2. Bot主动发送消息 → 使用Bot令牌');
  console.log('   3. 令牌过期 → 自动刷新');
  console.log('   4. 权限不足 → 智能回退');
  console.log('');
  console.log('💡 优势:');
  console.log('   - 更高的成功率（用户令牌权限更高）');
  console.log('   - 自动令牌管理（无需手动维护）');
  console.log('   - 智能回退机制（确保服务可用性）');
  console.log('   - 完整的生命周期管理');
}

// 运行测试
testUserTokenIntegration();
