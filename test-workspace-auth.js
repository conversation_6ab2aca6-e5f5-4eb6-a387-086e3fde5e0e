// 测试工作区鉴权系统
require('dotenv').config();
const axios = require('axios');

async function testWorkspaceAuth() {
  console.log('🔐 测试工作区鉴权系统');
  console.log('=' .repeat(60));

  const serverPort = process.env.PORT || 3002;
  const baseUrl = `http://localhost:${serverPort}`;

  try {
    // 1. 检查当前工作区列表
    console.log('1️⃣ 检查当前工作区列表...');
    const workspacesResponse = await axios.get(`${baseUrl}/slack/workspaces`);
    
    console.log('📋 工作区列表:');
    console.log('   总数:', workspacesResponse.data.total_workspaces);
    
    if (workspacesResponse.data.workspaces.length > 0) {
      workspacesResponse.data.workspaces.forEach((workspace, index) => {
        console.log(`   ${index + 1}. ${workspace.team_name} (${workspace.team_id})`);
        console.log(`      Bot用户ID: ${workspace.bot_user_id}`);
        console.log(`      授权用户: ${workspace.authed_user_id}`);
        console.log(`      权限范围: ${workspace.scope}`);
        console.log(`      有Token: ${workspace.has_token ? '✅' : '❌'}`);
        console.log(`      安装时间: ${workspace.installed_at}`);
        console.log('');
      });
    } else {
      console.log('   暂无已授权的工作区');
    }

    // 2. 模拟 OAuth 授权流程
    console.log('2️⃣ 模拟 OAuth 授权流程...');
    
    // 模拟 OAuth 回调数据
    const mockOAuthData = {
      code: 'mock_auth_code_12345',
      state: 'mock_state_67890'
    };

    console.log('🔗 模拟 OAuth 回调请求:');
    console.log('   授权码:', mockOAuthData.code);
    console.log('   状态:', mockOAuthData.state);

    // 注意：这里不会真正调用 OAuth 回调，因为需要真实的授权码
    console.log('   💡 实际测试需要真实的 Slack 授权流程');
    console.log('   💡 可以通过访问 /slack/oauth/start 开始授权');

    // 3. 测试健康检查中的 Slack 状态
    console.log('');
    console.log('3️⃣ 检查健康状态中的 Slack 信息...');
    
    const healthResponse = await axios.get(`${baseUrl}/health`);
    
    if (healthResponse.data.slack) {
      console.log('📊 Slack 状态:');
      console.log('   连接状态:', healthResponse.data.slack.success ? '✅ 成功' : '❌ 失败');
      
      if (healthResponse.data.slack.success) {
        console.log('   团队:', healthResponse.data.slack.team);
        console.log('   用户:', healthResponse.data.slack.user);
        console.log('   Bot ID:', healthResponse.data.slack.bot_id);
      } else {
        console.log('   错误:', healthResponse.data.slack.error);
      }
    }

    // 4. 模拟 Slack 事件处理
    console.log('');
    console.log('4️⃣ 模拟 Slack 事件处理...');
    
    const mockSlackEvent = {
      type: 'event_callback',
      team_id: 'T059DMNT0SW', // 从之前的测试中获取的团队ID
      event: {
        type: 'message',
        user: 'U0983BEK1HQ',
        text: '测试工作区鉴权系统',
        channel: 'D097W5SPBLM',
        ts: Date.now() / 1000
      }
    };

    console.log('📨 模拟事件数据:');
    console.log('   团队ID:', mockSlackEvent.team_id);
    console.log('   用户ID:', mockSlackEvent.event.user);
    console.log('   消息:', mockSlackEvent.event.text);

    try {
      const eventResponse = await axios.post(`${baseUrl}/slack/events`, mockSlackEvent, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (eventResponse.status === 200) {
        console.log('✅ 事件处理成功');
        console.log('   💡 系统会使用团队ID对应的Bot Token处理消息');
      } else {
        console.log('❌ 事件处理失败:', eventResponse.status);
      }
    } catch (eventError) {
      console.log('❌ 事件处理错误:', eventError.message);
      if (eventError.code === 'ECONNREFUSED') {
        console.log('   💡 请确保服务器正在运行: npm start');
      }
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('🔧 解决方案:');
      console.log('1. 启动服务器: npm start');
      console.log('2. 确认端口配置正确');
    }
  }

  console.log('');
  console.log('📊 工作区鉴权系统测试总结');
  console.log('=' .repeat(60));
  console.log('✅ 改进的功能:');
  console.log('   1. OAuth 回调保存工作区鉴权信息');
  console.log('   2. 根据团队ID使用对应的Bot Token');
  console.log('   3. 支持多工作区独立管理');
  console.log('   4. 提供工作区列表查看接口');
  console.log('');
  console.log('🎯 使用方法:');
  console.log('   1. 访问 /slack/oauth/start 开始授权');
  console.log('   2. 完成授权后查看 /slack/workspaces');
  console.log('   3. 发送消息时自动使用对应Token');
  console.log('');
  console.log('💡 生产环境建议:');
  console.log('   1. 使用数据库存储鉴权信息');
  console.log('   2. 实现Token刷新机制');
  console.log('   3. 添加鉴权信息加密');
  console.log('   4. 实现工作区管理界面');
}

// 运行测试
testWorkspaceAuth();
