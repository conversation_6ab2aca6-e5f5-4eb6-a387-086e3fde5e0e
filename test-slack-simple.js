// 简单的 Slack 测试脚本
require('dotenv').config();
const axios = require('axios');

async function testSlackSimple() {
  console.log('🧪 简单 Slack 测试...');
  
  // 测试 URL 验证
  const urlVerificationEvent = {
    type: 'url_verification',
    challenge: 'test_challenge_123'
  };

  try {
    console.log('📨 测试 URL 验证...');
    const response = await axios.post('http://localhost:3005/slack/events', urlVerificationEvent, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });

    console.log('✅ URL 验证响应:', response.data);
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('服务器未运行在端口 3005');
    }
  }
}

testSlackSimple();
