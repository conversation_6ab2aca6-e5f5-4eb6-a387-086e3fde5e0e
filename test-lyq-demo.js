// index.js
require('dotenv').config(); // 加载环境变量
const { ZepClient } = require('@getzep/zep-cloud');
const { v4: uuidv4 } = require('uuid'); // 用于生成唯一ID

// --- 配置 ---
// 确保您已设置 ZEP_API_KEY 环境变量
const apiKey = process.env.ZEP_API_KEY;
if (!apiKey) {
    throw new Error("请设置 ZEP_API_KEY 环境变量");
}
// 为我们的知识图谱定义一个唯一的用户ID
const userId = `router_demo_user_${uuidv4()}`;


// --- 主流程函数 ---
async function main() {
    console.log("--- 1. 初始化 Zep 客户端 ---");
    const client = new ZepClient({ apiKey });

    try {
        console.log(`\n--- 2. 创建用户: ${userId} ---`);
        // 创建用户用于知识图谱
        const user = await client.user.add({
            userId: userId,
            email: '<EMAIL>',
            firstName: '路由器',
            lastName: '演示用户',
            metadata: {
                source: 'router-knowledge-demo',
                created: new Date().toISOString()
            }
        });
        console.log("用户创建成功!");

        // --- 3. 数据入库 (畅游网络随身路由器业务知识图谱) ---
        console.log("\n--- 3. 准备并添加畅游网络业务数据 ---");
        const businessData = [
            // 产品型号数据
            {
                documentId: "changyou-mini",
                content: "畅游 Mini - 极致便携的随身路由器，适合个人短期出行，8-10小时续航，支持5台设备连接，4G LTE网络技术。",
                metadata: {
                    type: "Product",
                    brand: "畅游网络",
                    model: "畅游 Mini",
                    price: 299,
                    battery_hours: "8-10",
                    max_devices: 5,
                    network_tech: "4G LTE",
                    target_users: ["个人旅行者", "背包客"],
                    features: ["极致便携", "个人出行", "基础连接"]
                }
            },
            {
                documentId: "changyou-pro",
                content: "畅游 Pro - 平衡性能与续航的商务路由器，15-18小时续航，支持10台设备，5G/4G智能切换，适合商务差旅。",
                metadata: {
                    type: "Product",
                    brand: "畅游网络",
                    model: "畅游 Pro",
                    price: 599,
                    battery_hours: "15-18",
                    max_devices: 10,
                    network_tech: "5G / 4G LTE 智能切换",
                    target_users: ["商务人士", "留学生"],
                    features: ["智能切换", "商务差旅", "平衡性能"]
                }
            },
            {
                documentId: "changyou-max",
                content: "畅游 Max - 旗舰性能随身路由器，20-24小时超长续航，支持16台设备，全球主流5G/4G频段，适合家庭团队出游。",
                metadata: {
                    type: "Product",
                    brand: "畅游网络",
                    model: "畅游 Max",
                    price: 999,
                    battery_hours: "20-24",
                    max_devices: 16,
                    network_tech: "全球主流 5G / 4G LTE 频段",
                    target_users: ["家庭出游", "小型商务团队"],
                    features: ["旗舰性能", "多设备支持", "全球频段"]
                }
            },
            // 流量套餐数据
            {
                documentId: "data-plans-monthly",
                content: "畅游网络月度流量套餐：128元/50GB、198元/100GB、288元/200GB，适合常规用户的上网需求。",
                metadata: {
                    type: "DataPlan",
                    category: "月度套餐",
                    plans: [
                        { price: 128, data: "50GB", target: "常规用户" },
                        { price: 198, data: "100GB", target: "常规用户" },
                        { price: 288, data: "200GB", target: "重度用户" }
                    ],
                    features: ["按月计费", "灵活选择", "适合常规使用"]
                }
            },
            {
                documentId: "data-plans-yearly",
                content: "畅游网络年度套餐性价比之选：1299元/600GB（相当于月108元）、1999元/1200GB（相当于月166元），老用户续费享85折。",
                metadata: {
                    type: "DataPlan",
                    category: "年度套餐",
                    plans: [
                        { price: 1299, data: "600GB", monthly_equivalent: 108 },
                        { price: 1999, data: "1200GB", monthly_equivalent: 166 }
                    ],
                    features: ["性价比高", "老用户85折", "长期优惠"]
                }
            },
            {
                documentId: "service-policy-presale",
                content: "畅游网络售前政策：免费咨询服务、需求评估、透明资费说明、7天无理由试用、24小时内发货承诺。",
                metadata: {
                    type: "ServicePolicy",
                    category: "售前政策",
                    services: ["免费咨询", "需求评估", "透明资费", "7天试用", "快速发货"],
                    features: ["专业咨询", "无理由退货", "快速响应"]
                }
            },
            {
                documentId: "service-policy-aftersale",
                content: "畅游网络售后政策：一年硬件保修、终身技术支持、流量管理提醒、7x12小时在线支持、设备丢失保护。",
                metadata: {
                    type: "ServicePolicy",
                    category: "售后政策",
                    services: ["一年保修", "终身技术支持", "流量提醒", "在线支持", "丢失保护"],
                    features: ["全面保障", "持续支持", "用户安心"]
                }
            }
        ];

        // 将畅游网络业务数据添加到知识图谱
        console.log("开始添加畅游网络业务数据到知识图谱...");
        for (const item of businessData) {
            // 构建知识图谱数据
            const knowledgeData = {
                type: 'json',
                data: JSON.stringify({
                    entity_type: item.metadata.type.toLowerCase(),
                    id: item.documentId,
                    description: item.content,
                    ...item.metadata
                })
            };

            await client.graph.add({
                userId: userId,
                ...knowledgeData
            });
        }
        console.log(`${businessData.length} 个业务数据项已成功添加到知识图谱!`);

        // --- 4. 智能查询 (畅游网络业务场景) ---
        console.log("\n--- 4. 执行智能查询 ---");

        // 等待知识图谱数据处理
        console.log("⏳ 等待知识图谱数据处理...");
        await new Promise(resolve => setTimeout(resolve, 5000));

        // 场景1: 商务人士咨询产品推荐
        const userQuery1 = "我是商务人士，经常出差，需要一个续航长、支持多设备的随身路由器，预算600元左右";
        console.log(`\n用户查询1: "${userQuery1}"`);

        const searchResults1 = await client.graph.search({
            userId: userId,
            query: userQuery1
        });

        console.log("\n--- 查询结果1 (产品推荐) ---");
        if (searchResults1.edges && searchResults1.edges.length > 0) {
            searchResults1.edges.slice(0, 3).forEach((edge, index) => {
                console.log(`${index + 1}. ${edge.fact}`);
            });
        } else {
            console.log("暂无相关结果（数据可能还在处理中）");
        }

        // 场景2: 用户咨询流量套餐
        const userQuery2 = "我需要了解你们的流量套餐，有什么优惠活动吗？";
        console.log(`\n用户查询2: "${userQuery2}"`);

        const searchResults2 = await client.graph.search({
            userId: userId,
            query: userQuery2
        });

        console.log("\n--- 查询结果2 (流量套餐) ---");
        if (searchResults2.edges && searchResults2.edges.length > 0) {
            searchResults2.edges.slice(0, 3).forEach((edge, index) => {
                console.log(`${index + 1}. ${edge.fact}`);
            });
        } else {
            console.log("暂无相关结果（数据可能还在处理中）");
        }

        // 场景3: 用户咨询售后服务
        const userQuery3 = "如果设备出现问题，你们的售后服务怎么样？";
        console.log(`\n用户查询3: "${userQuery3}"`);

        const searchResults3 = await client.graph.search({
            userId: userId,
            query: userQuery3
        });

        console.log("\n--- 查询结果3 (售后服务) ---");
        if (searchResults3.edges && searchResults3.edges.length > 0) {
            searchResults3.edges.slice(0, 3).forEach((edge, index) => {
                console.log(`${index + 1}. ${edge.fact}`);
            });
        } else {
            console.log("暂无相关结果（数据可能还在处理中）");
        }

    } catch (error) {
        console.error("执行过程中发生错误:", error);
    } finally {
        // --- 5. 清理 ---
        console.log(`\n--- 5. 清理演示数据: ${userId} ---`);
        try {
            await client.user.delete(userId);
            console.log("演示数据清理成功!");
        } catch (cleanupError) {
            console.error("清理演示数据时出错:", cleanupError.message);
        }
    }
}

// 运行主函数
main();