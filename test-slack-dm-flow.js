// 测试 Slack 私聊流程
require('dotenv').config();
const axios = require('axios');

async function testSlackDMFlow() {
  console.log('🧪 测试 Slack 私聊流程...');
  console.log('=' .repeat(50));

  const testUserId = 'U053CTYEARZ'; // 从错误日志中获取的用户ID
  const serverPort = process.env.PORT || 3002;

  try {
    // 1. 测试 conversations.open API
    console.log('🔗 步骤 1: 测试打开私聊通道...');
    const openResponse = await axios.post('https://slack.com/api/conversations.open', {
      users: testUserId
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (openResponse.data.ok) {
      const channelId = openResponse.data.channel.id;
      console.log('✅ 私聊通道打开成功');
      console.log(`   通道ID: ${channelId}`);
      console.log(`   是否为新通道: ${openResponse.data.channel.created ? '是' : '否'}`);

      // 2. 测试发送消息到新通道
      console.log('');
      console.log('💬 步骤 2: 测试发送消息到新通道...');
      const messageResponse = await axios.post('https://slack.com/api/chat.postMessage', {
        channel: channelId,
        text: '🧪 测试消息：私聊通道流程验证成功！'
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      if (messageResponse.data.ok) {
        console.log('✅ 消息发送成功');
        console.log(`   消息时间戳: ${messageResponse.data.ts}`);
      } else {
        console.log('❌ 消息发送失败:', messageResponse.data.error);
      }

      // 3. 测试完整的事件处理流程
      console.log('');
      console.log('🔄 步骤 3: 测试完整的事件处理流程...');
      
      const mockEvent = {
        type: 'event_callback',
        event: {
          type: 'message',
          user: testUserId,
          text: '测试私聊流程',
          channel: channelId, // 使用新的通道ID
          ts: Date.now() / 1000
        }
      };

      try {
        const eventResponse = await axios.post(`http://localhost:${serverPort}/slack/events`, mockEvent, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });

        if (eventResponse.status === 200) {
          console.log('✅ 事件处理成功');
          console.log('   等待服务器处理并回复...');
          
          // 等待一下让服务器处理完成
          await new Promise(resolve => setTimeout(resolve, 3000));
        } else {
          console.log('❌ 事件处理失败:', eventResponse.status);
        }
      } catch (eventError) {
        console.log('❌ 事件处理错误:', eventError.message);
        if (eventError.code === 'ECONNREFUSED') {
          console.log('   💡 请确保服务器正在运行: npm start');
        }
      }

    } else {
      console.log('❌ 打开私聊通道失败:', openResponse.data.error);
      
      if (openResponse.data.error === 'missing_scope') {
        console.log('');
        console.log('🔧 解决方案:');
        console.log('1. 在 Slack App 设置中添加 im:write 权限');
        console.log('2. 重新安装 App 到工作区');
        console.log('3. 确保 Bot Token 已更新');
      }
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
    
    if (error.response) {
      console.log('   HTTP状态:', error.response.status);
      console.log('   错误详情:', error.response.data);
    }
  }

  console.log('');
  console.log('📋 私聊流程说明:');
  console.log('1. 接收到私信事件时，提取用户ID');
  console.log('2. 调用 conversations.open 获取私聊通道ID');
  console.log('3. 使用新的通道ID发送回复消息');
  console.log('4. 这样可以确保消息正确发送到私聊');
  console.log('');
  console.log('🎯 关键权限要求:');
  console.log('- im:write: 必需，用于打开和发送私信');
  console.log('- chat:write: 必需，用于发送消息');
  console.log('- users:read: 建议，用于获取用户信息');
  console.log('');
  console.log('🎉 私聊流程测试完成!');
}

// 运行测试
testSlackDMFlow();
