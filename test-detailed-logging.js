// 测试详细日志功能
require('dotenv').config();
const axios = require('axios');

async function testDetailedLogging() {
  console.log('📊 测试 sendSlackMessage 详细日志功能');
  console.log('=' .repeat(60));

  const serverPort = process.env.PORT || 3002;
  const baseUrl = `http://localhost:${serverPort}`;

  // 测试场景1: 模拟正常的Slack事件（会触发sendSlackMessage）
  console.log('1️⃣ 测试场景1: 模拟Slack消息事件');
  console.log('   这将触发完整的sendSlackMessage流程并显示详细日志');
  console.log('');

  const mockSlackEvent = {
    type: 'event_callback',
    team_id: 'T059DMNT0SW',
    event: {
      type: 'message',
      user: 'U0983BEK1HQ',
      text: '测试详细日志功能 - 这是一条用于验证日志记录的测试消息',
      channel: 'D097W5SPBLM',
      ts: Date.now() / 1000
    }
  };

  console.log('📨 发送模拟Slack事件:');
  console.log('   团队ID:', mockSlackEvent.team_id);
  console.log('   用户ID:', mockSlackEvent.event.user);
  console.log('   频道ID:', mockSlackEvent.event.channel);
  console.log('   消息内容:', mockSlackEvent.event.text);
  console.log('');

  try {
    console.log('🚀 发送事件到服务器...');
    console.log('   URL:', `${baseUrl}/slack/events`);
    console.log('   请注意观察服务器端的详细日志输出');
    console.log('');

    const eventResponse = await axios.post(`${baseUrl}/slack/events`, mockSlackEvent, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    if (eventResponse.status === 200) {
      console.log('✅ 事件发送成功，状态码:', eventResponse.status);
      console.log('   服务器应该已经显示了详细的sendSlackMessage日志');
    } else {
      console.log('❌ 事件发送失败，状态码:', eventResponse.status);
    }

  } catch (error) {
    console.log('❌ 发送事件失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('');
      console.log('🔧 解决方案:');
      console.log('1. 启动服务器: npm start');
      console.log('2. 确认端口配置正确');
      console.log('3. 检查防火墙设置');
      return;
    }
  }

  // 等待一下让服务器处理完成
  console.log('');
  console.log('⏳ 等待服务器处理完成...');
  await new Promise(resolve => setTimeout(resolve, 3000));

  console.log('');
  console.log('📊 详细日志功能说明');
  console.log('=' .repeat(60));
  console.log('✅ 新增的日志内容包括:');
  console.log('');
  console.log('🔑 Token获取流程:');
  console.log('   - 步骤1: 根据用户ID获取Token');
  console.log('   - 步骤2: 根据团队ID获取Token');
  console.log('   - 步骤3: 使用全局Token作为回退');
  console.log('   - Token来源、长度、前缀信息');
  console.log('');
  console.log('📺 频道处理流程:');
  console.log('   - 原始频道ID和类型判断');
  console.log('   - 私信通道打开过程');
  console.log('   - 频道ID变更记录');
  console.log('');
  console.log('🌐 API请求详情:');
  console.log('   - 请求准备和数据构建');
  console.log('   - API调用耗时统计');
  console.log('   - 响应状态和数据');
  console.log('');
  console.log('✅ 成功情况日志:');
  console.log('   - 消息时间戳');
  console.log('   - 实际发送频道');
  console.log('   - Token来源信息');
  console.log('   - 总处理时间');
  console.log('');
  console.log('❌ 错误情况日志:');
  console.log('   - 详细错误分析');
  console.log('   - 权限问题诊断');
  console.log('   - 解决建议');
  console.log('   - 调试信息汇总');
  console.log('');
  console.log('⏱️ 性能监控:');
  console.log('   - 各阶段耗时统计');
  console.log('   - API调用时间');
  console.log('   - 总处理时间');
  console.log('');
  console.log('🎯 日志优势:');
  console.log('   1. 完整的执行流程追踪');
  console.log('   2. 详细的错误诊断信息');
  console.log('   3. 性能监控和优化指导');
  console.log('   4. 调试友好的信息输出');
  console.log('   5. 生产环境问题定位');
  console.log('');
  console.log('💡 使用建议:');
  console.log('   - 开发环境: 保持详细日志便于调试');
  console.log('   - 生产环境: 可考虑调整日志级别');
  console.log('   - 监控告警: 基于错误日志设置告警');
  console.log('   - 性能优化: 关注耗时统计数据');
}

// 显示日志格式示例
function showLogExample() {
  console.log('');
  console.log('📋 详细日志输出示例');
  console.log('=' .repeat(60));
  console.log(`
📤 开始发送Slack消息
   参数信息:
     频道ID: D097W5SPBLM
     消息长度: 45
     用户ID: U0983BEK1HQ
     团队ID: T059DMNT0SW
     开始时间: 2024-01-15T10:30:00.000Z

🔑 开始Token获取流程...
   步骤1: 尝试根据用户ID获取Token...
   ✅ 根据用户ID获取Token成功

🔑 Token获取完成:
   Token来源: 用户ID(U0983BEK1HQ)
   Token长度: 56
   Token前缀: xoxb-53197...

📺 频道处理流程...
   原始频道ID: D097W5SPBLM
   是否为私信频道: true
   是否提供用户ID: true
   🔗 检测到私信，尝试打开私聊通道...
   ✅ 私聊通道打开成功:
     新频道ID: D098ABC123XYZ
     耗时: 245ms

📤 准备发送API请求:
   URL: https://slack.com/api/chat.postMessage
   最终频道ID: D098ABC123XYZ
   消息内容预览: 您好！欢迎咨询畅游网络随身路由器业务...

🌐 发送API请求...

📥 收到API响应:
   HTTP状态: 200
   API耗时: 156ms
   总耗时: 401ms

✅ Slack消息发送成功:
   消息时间戳: 1234567890.123456
   实际频道: D098ABC123XYZ
   Token来源: 用户ID(U0983BEK1HQ)
   总处理时间: 401ms
  `);
}

// 运行测试
async function runTest() {
  await testDetailedLogging();
  showLogExample();
  
  console.log('');
  console.log('🎉 详细日志功能测试完成！');
  console.log('');
  console.log('📝 下一步:');
  console.log('1. 启动服务器观察实际日志输出');
  console.log('2. 发送真实Slack消息测试');
  console.log('3. 根据日志信息优化性能');
  console.log('4. 设置生产环境日志级别');
}

runTest();
