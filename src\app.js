// WhatsApp、Slack与LINE智能客服集成 - 重构版本
require('dotenv').config();
const express = require('express');
const twilio = require('twilio');
const axios = require('axios');
const line = require('@line/bot-sdk');
const crypto = require('crypto');

// 导入服务模块
const MessageProcessingService = require('./services/messageProcessingService');
const KnowledgeGraphService = require('./services/knowledgeGraphService');
const SlackAuthDatabase = require('./database/slackAuth');
const TokenManager = require('./services/tokenManager');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3002;

// 初始化服务
const messageProcessor = new MessageProcessingService();
const knowledgeService = new KnowledgeGraphService();
const slackAuthDB = new SlackAuthDatabase();
const tokenManager = new TokenManager();

// 初始化Twilio客户端
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// 初始化LINE客户端
let lineClient = null;
try {
  if (process.env.LINE_CHANNEL_ACCESS_TOKEN) {
    lineClient = new line.messagingApi.MessagingApiClient({
      channelAccessToken: process.env.LINE_CHANNEL_ACCESS_TOKEN
    });
    console.log('✅ LINE客户端初始化成功');
  } else {
    console.log('⚠️ LINE_CHANNEL_ACCESS_TOKEN未配置，LINE功能将被禁用');
  }
} catch (error) {
  console.error('❌ LINE客户端初始化失败:', error.message);
  lineClient = null;
}

// LINE中间件配置
const lineConfig = {
  channelSecret: process.env.LINE_CHANNEL_SECRET || '2e4c782ab842a7d9ac469a57ee9a3521'
};

// 保存工作区鉴权信息 (使用SQLite数据库)
async function saveWorkspaceAuth(authData) {
  try {
    await slackAuthDB.saveWorkspaceAuth(authData);

    console.log('💾 保存工作区鉴权信息到数据库:');
    console.log('   团队ID:', authData.team_id);
    console.log('   团队名:', authData.team_name);
    console.log('   Bot用户ID:', authData.bot_user_id);
    console.log('   授权用户:', authData.authed_user_id);
    console.log('   权限范围:', authData.scope);
  } catch (error) {
    console.error('❌ 保存工作区鉴权信息失败:', error.message);
    throw error;
  }
}

// 根据团队ID获取对应的Bot Token
async function getBotTokenForTeam(team_id) {
  if (team_id) {
    try {
      const token = await slackAuthDB.getBotTokenForTeam(team_id);
      if (token) {
        return token;
      }
    } catch (error) {
      console.error('❌ 从数据库获取Bot Token失败:', error.message);
    }
  }

  // 回退到全局Token（兼容性）
  return process.env.SLACK_BOT_TOKEN;
}

// 根据用户ID获取对应的Bot Token
async function getBotTokenForUser(user_id) {
  if (user_id) {
    try {
      const token = await slackAuthDB.getBotTokenForUser(user_id);
      if (token) {
        return token;
      }
    } catch (error) {
      console.error('❌ 根据用户ID获取Bot Token失败:', error.message);
    }
  }

  // 回退到全局Token（兼容性）
  return process.env.SLACK_BOT_TOKEN;
}

// 解析表单数据（Twilio发送的是form-encoded数据）
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Slack 事件验证中间件
function verifySlackRequest(req, res, next) {
  // 对于 URL verification challenge，直接处理
  if (req.body && req.body.type === 'url_verification') {
    return res.json({ challenge: req.body.challenge });
  }

  // 这里可以添加更严格的 Slack 签名验证
  // 目前为了简化，我们跳过签名验证
  next();
}

// Webhook端点 - 接收WhatsApp消息
app.post('/whatsapp-webhook', async (req, res) => {
  console.log('\n📱 收到WhatsApp消息!');
  console.log('=' .repeat(50));

  const {
    MessageSid,
    From,
    Body,
    ProfileName
  } = req.body;

  // 记录消息详情
  console.log('📋 消息详情:');
  console.log('   消息ID:', MessageSid);
  console.log('   发送方:', From);
  console.log('   内容:', Body);
  console.log('   发送者姓名:', ProfileName || 'Unknown');
  console.log('   接收时间:', new Date().toLocaleString());

  // 准备TwiML响应
  const twiml = new twilio.twiml.MessagingResponse();

  // 立即返回响应，避免Twilio超时
  res.writeHead(200, {'Content-Type': 'text/xml'});
  res.end(twiml.toString());

  try {
    // 使用消息处理服务处理用户消息
    const result = await messageProcessor.processMessage(From, Body, ProfileName);

    if (result.success) {
      // 发送回复到WhatsApp
      await sendWhatsAppMessage(From, result.response);

      console.log(`✅ 消息处理成功 - 意图: ${result.intent}, 使用知识: ${result.usedKnowledge}`);
    } else {
      // 发送错误回复
      await sendWhatsAppMessage(From, result.response);
      console.log(`❌ 消息处理失败: ${result.error}`);
    }

  } catch (error) {
    console.error('❌ 处理消息失败:', error.message);
    // 发送错误通知
    await sendWhatsAppMessage(
      From,
      '很抱歉，处理您的消息时出现了问题。请稍后再试。'
    );
  }
});

// LINE Webhook端点 - 接收LINE消息
app.post('/line-webhook', async (req, res) => {
  console.log('\n📱 收到LINE消息!');
  console.log('=' .repeat(50));

  // 验证签名
  const signature = req.get('x-line-signature');
  if (!signature) {
    console.log('❌ 缺少LINE签名');
    return res.status(401).send('Unauthorized');
  }

  // 验证请求签名
  const body = JSON.stringify(req.body);
  const expectedSignature = crypto
    .createHmac('sha256', lineConfig.channelSecret)
    .update(body)
    .digest('base64');

  if (signature !== expectedSignature) {
    console.log('❌ LINE签名验证失败');
    return res.status(401).send('Unauthorized');
  }

  // 立即返回200响应
  res.status(200).end();

  try {
    // 处理所有事件
    const events = req.body.events || [];
    console.log(`📋 收到 ${events.length} 个事件`);

    for (const event of events) {
      console.log('📋 事件详情:', {
        type: event.type,
        timestamp: event.timestamp,
        source: event.source
      });

      // 处理消息事件
      if (event.type === 'message' && event.message.type === 'text') {
        const {
          replyToken,
          message: { text },
          source: { userId }
        } = event;

        console.log('📋 消息详情:');
        console.log('   用户ID:', userId);
        console.log('   内容:', text);
        console.log('   回复Token:', replyToken);
        console.log('   接收时间:', new Date().toLocaleString());

        // 使用消息处理服务处理用户消息
        const lineUserId = `line:${userId}`;
        const result = await messageProcessor.processMessage(lineUserId, text, userId);

        if (result.success) {
          // 发送回复到LINE
          await sendLineMessage(replyToken, result.response);
          console.log(`✅ LINE消息处理成功 - 意图: ${result.intent}, 使用知识: ${result.usedKnowledge}`);
        } else {
          // 发送错误回复
          await sendLineMessage(replyToken, result.response);
          console.log(`❌ LINE消息处理失败: ${result.error}`);
        }
      } else if (event.type === 'follow') {
        // 处理用户关注事件
        console.log('👥 用户关注了LINE Official Account');
        const welcomeMessage = '欢迎关注畅游网络智能客服！我可以帮助您解答各种问题。';
        await sendLineMessage(event.replyToken, welcomeMessage);
      } else if (event.type === 'unfollow') {
        // 处理用户取消关注事件
        console.log('👋 用户取消关注了LINE Official Account');
      } else {
        console.log(`ℹ️ 忽略事件类型: ${event.type}`);
      }
    }

  } catch (error) {
    console.error('❌ 处理LINE消息失败:', error.message);
  }
});

// Slack 事件处理端点
app.post('/slack/events', verifySlackRequest, async (req, res) => {
  console.log('\n💬 收到Slack事件!');
  console.log('=' .repeat(50));

  const { type, event, team_id } = req.body;

  // 处理 URL verification challenge
  if (type === 'url_verification') {
    console.log('🔐 处理Slack URL验证挑战');
    return res.json({ challenge: req.body.challenge });
  }

  // 立即响应 Slack，避免重复发送
  res.status(200).send('OK');

  // 处理消息事件
  if (type === 'event_callback' && event && event.type === 'message') {
    // 忽略机器人自己的消息
    if (event.bot_id) {
      console.log('🤖 忽略机器人消息');
      return;
    }

    // 忽略消息编辑和删除事件
    if (event.subtype && ['message_changed', 'message_deleted'].includes(event.subtype)) {
      console.log('📝 忽略消息编辑/删除事件');
      return;
    }

    const {
      user: userId,
      text: messageText,
      channel: channelId,
      ts: timestamp
    } = event;

    // 记录消息详情
    console.log('📋 Slack消息:', { userId, channelId, text: messageText.substring(0, 50) + '...', timestamp });

    try {
      // 保存用户和工作区的映射关系
      if (team_id && userId) {
        await slackAuthDB.saveUserMapping(userId, team_id, channelId);
      }

      // 使用现有的消息处理服务处理用户消息
      // 将 Slack 用户ID 作为 from 参数，用户名暂时使用用户ID
      const slackUserId = `slack:${userId}`;
      const result = await messageProcessor.processMessage(slackUserId, messageText, userId);

      if (result.success) {
        // 发送回复到 Slack，传递用户ID和团队ID以便处理私信
        await sendSlackMessage(channelId, result.response, userId, team_id);

        console.log(`✅ Slack消息处理成功 - 意图: ${result.intent}, 使用知识: ${result.usedKnowledge}`);
      } else {
        // 发送错误回复
        await sendSlackMessage(channelId, result.response, userId, team_id);
        console.log(`❌ Slack消息处理失败: ${result.error}`);
      }

    } catch (error) {
      console.error('❌ 处理Slack消息失败:', error.message);

      // 根据错误类型提供不同的处理
      if (error.message.includes('channel_not_found') || error.message.includes('not_in_channel') || error.message.includes('missing_scope')) {
        console.log('🔧 Bot权限问题:', error.message);
        console.log('💡 需要在 Slack App 中添加权限: im:write, channels:read, groups:read, im:read, users:read');
        // 对于权限问题，不再尝试发送错误消息，避免无限循环
      } else {
        // 对于其他错误，尝试发送错误通知
        try {
          await sendSlackMessage(
            channelId,
            '很抱歉，处理您的消息时出现了问题。请稍后再试。',
            userId,
            team_id
          );
        } catch (sendError) {
          console.error('❌ 发送错误消息也失败了:', sendError.message);
        }
      }
    }
  }
});

// Slack OAuth 回调端点
app.get('/slack/oauth/callback', async (req, res) => {
  console.log('\n🔐 收到Slack OAuth回调!');
  console.log('=' .repeat(50));

  const { code, state, error } = req.query;

  // 处理授权错误
  if (error) {
    console.log('❌ OAuth授权失败:', error);
    return res.status(400).json({
      success: false,
      error: 'OAuth authorization failed',
      details: error
    });
  }

  // 检查是否有授权码
  if (!code) {
    console.log('❌ 缺少授权码');
    return res.status(400).json({
      success: false,
      error: 'Missing authorization code'
    });
  }

  console.log('📋 OAuth回调详情:');
  console.log('   授权码:', code.substring(0, 20) + '...');
  console.log('   状态:', state || 'N/A');
  console.log('   接收时间:', new Date().toLocaleString());

  try {
    // 使用授权码换取访问令牌
    const requestData = new URLSearchParams({
      client_id: process.env.SLACK_CLIENT_ID,
      client_secret: process.env.SLACK_CLIENT_SECRET,
      code: code,
      redirect_uri: process.env.SLACK_REDIRECT_URI
    });
    const requestHeaders = {
      'Content-Type': 'application/x-www-form-urlencoded'
    };

    console.log('🔑 Slack API Request - oauth.v2.access:');
    console.log('   URL:', 'https://slack.com/api/oauth.v2.access');
    console.log('   Data:', requestData.toString().replace(/client_secret=[^&]+/, 'client_secret=[HIDDEN]'));
    console.log('   Headers:', requestHeaders);

    const tokenResponse = await axios.post('https://slack.com/api/oauth.v2.access', requestData, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - oauth.v2.access:');
    console.log('   Status:', tokenResponse.status);
    console.log('   Data:', JSON.stringify({
      ...tokenResponse.data,
      access_token: tokenResponse.data.access_token ? '[HIDDEN]' : undefined,
      bot_user_access_token: tokenResponse.data.bot_user_access_token ? '[HIDDEN]' : undefined
    }));

    if (tokenResponse.data.ok) {
      const {
        access_token,
        bot_user_id,
        team,
        authed_user,
        scope,
        bot_user_access_token
      } = tokenResponse.data;

      console.log('✅ OAuth令牌获取成功!',tokenResponse.data);
      

      // 使用 TokenManager 保存完整的OAuth信息
      await tokenManager.saveOAuthData(tokenResponse.data);

      console.log('💾 OAuth令牌信息已保存（包含用户令牌和刷新令牌）');

      // 返回成功页面
      res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Slack 授权成功</title>
          <meta charset="utf-8">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .success { color: #28a745; }
            .info { color: #6c757d; margin-top: 20px; }
          </style>
        </head>
        <body>
          <h1 class="success">🎉 Slack 授权成功!</h1>
          <p>畅游网络智能客服已成功连接到您的 Slack 工作区。</p>
          <div class="info">
            <p><strong>团队:</strong> ${team?.name || 'N/A'}</p>
            <p><strong>Bot ID:</strong> ${bot_user_id || 'N/A'}</p>
            <p><strong>权限:</strong> ${scope || 'N/A'}</p>
          </div>
          <p>您现在可以关闭此页面，开始在 Slack 中使用智能客服。</p>
        </body>
        </html>
      `);

    } else {
      throw new Error(`Slack API错误: ${tokenResponse.data.error}`);
    }

  } catch (error) {
    console.error('❌ OAuth令牌交换失败:', error.message);

    res.status(500).json({
      success: false,
      error: 'Failed to exchange authorization code for token',
      details: error.message
    });
  }
});

// Slack OAuth 授权启动端点
app.get('/slack/oauth/start', (req, res) => {
  console.log('\n🚀 启动Slack OAuth授权流程');

  // 检查必要的环境变量
  if (!process.env.SLACK_CLIENT_ID) {
    return res.status(500).json({
      success: false,
      error: 'SLACK_CLIENT_ID not configured'
    });
  }

  if (!process.env.SLACK_REDIRECT_URI) {
    return res.status(500).json({
      success: false,
      error: 'SLACK_REDIRECT_URI not configured'
    });
  }

  // 生成状态参数（可选，用于防止CSRF攻击）
  const state = Math.random().toString(36).substring(2, 15);

  // 构建 Slack OAuth URL
  const scopes = [
    'chat:write',
    'channels:read',
    'groups:read',
    'im:read',
    'users:read',
    'bot'
  ].join(',');

  const authUrl = `https://slack.com/oauth/v2/authorize?` +
    `client_id=${process.env.SLACK_CLIENT_ID}&` +
    `scope=${encodeURIComponent(scopes)}&` +
    `redirect_uri=${encodeURIComponent(process.env.SLACK_REDIRECT_URI)}&` +
    `state=${state}`;

  console.log('🔗 重定向到Slack授权页面');
  console.log('   Client ID:', process.env.SLACK_CLIENT_ID);
  console.log('   Redirect URI:', process.env.SLACK_REDIRECT_URI);
  console.log('   Scopes:', scopes);
  console.log('   State:', state);

  // 重定向到 Slack 授权页面
  res.redirect(authUrl);
});

// 发送WhatsApp消息
async function sendWhatsAppMessage(to, body) {
  try {
    console.log(`📤 发送WhatsApp消息到 ${to}:`);
    console.log(`   ${body.substring(0, 100)}${body.length > 100 ? '...' : ''}`);

    const message = await twilioClient.messages.create({
      from: 'whatsapp:+14155238886',
      body: body,
      to: to
    });

    console.log(`✅ 消息发送成功: ${JSON.stringify(message)}`);
    return message;

  } catch (error) {
    console.error('❌ 发送WhatsApp消息失败:', error.message);
    throw error;
  }
}

// 发送LINE消息
async function sendLineMessage(replyToken, text) {
  try {
    if (!lineClient) {
      console.error('❌ LINE客户端未初始化，无法发送消息');
      throw new Error('LINE客户端未初始化');
    }

    console.log(`📤 发送LINE消息:`);
    console.log(`   回复Token: ${replyToken}`);
    console.log(`   内容: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);

    const message = {
      type: 'text',
      text: text
    };

    const response = await lineClient.replyMessage({
      replyToken: replyToken,
      messages: [message]
    });

    console.log(`✅ LINE消息发送成功`);
    return response;

  } catch (error) {
    console.error('❌ 发送LINE消息失败:', error.message);

    // 详细的错误分析
    if (error.response) {
      console.error('   HTTP状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }

    // 针对不同错误类型提供更详细的错误信息
    if (error.message.includes('Invalid reply token')) {
      console.error('   💡 建议: Reply token已过期或无效，请检查token是否正确');
    } else if (error.message.includes('Invalid channel access token')) {
      console.error('   💡 建议: Channel access token无效，请检查环境变量配置');
    }

    throw error;
  }
}

// 使用用户令牌打开私聊通道
async function openDirectMessageWithUserToken(userId) {
  try {
    console.log('🔑 使用用户令牌打开私聊通道...');
    console.log('   目标用户ID:', userId);

    // 获取用户的访问令牌
    const userToken = await tokenManager.getUserToken(userId);

    if (!userToken) {
      throw new Error(`无法获取用户 ${userId} 的访问令牌`);
    }

    console.log('✅ 获取到用户令牌，长度:', userToken.length);

    const requestData = { users: userId };
    const requestHeaders = {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json'
    };

    console.log('🔗 Slack API Request - conversations.open (用户令牌):');
    console.log('   URL:', 'https://slack.com/api/conversations.open');
    console.log('   Data:', JSON.stringify(requestData));
    console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [USER_TOKEN_HIDDEN]' });

    const response = await axios.post('https://slack.com/api/conversations.open', requestData, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - conversations.open (用户令牌):');
    console.log('   Status:', response.status);
    console.log('   Data:', JSON.stringify(response.data));

    if (response.data.ok) {
      const channelId = response.data.channel.id;
      console.log('✅ 使用用户令牌打开私聊通道成功:', channelId);
      return channelId;
    } else {
      throw new Error(`使用用户令牌打开私聊通道失败: ${response.data.error}`);
    }
  } catch (error) {
    console.error('❌ 使用用户令牌打开私聊通道失败:', error.message);
    throw error;
  }
}

// 验证用户ID是否存在
async function validateSlackUser(userId, botToken) {
  try {
    console.log('👤 验证用户ID:', userId);

    const response = await axios.post('https://slack.com/api/users.info', {
      user: userId
    }, {
      headers: {
        'Authorization': `Bearer ${botToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📥 用户验证响应:', JSON.stringify(response.data));

    if (response.data.ok) {
      console.log('✅ 用户验证成功:', response.data.user.name);
      return true;
    } else {
      console.log('❌ 用户验证失败:', response.data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ 用户验证异常:', error.message);
    return false;
  }
}

// 打开与用户的私聊通道
async function openDirectMessage(userId, team_id = null) {
  try {
    console.log('🔗 开始打开私聊通道流程...');
    console.log('   目标用户ID:', userId);
    console.log('   团队ID:', team_id || 'null');

    // 优先根据用户ID获取Token，如果没有则根据团队ID获取
    let botToken;
    if (userId) {
      botToken = await getBotTokenForUser(userId);
    }
    if (!botToken && team_id) {
      botToken = await getBotTokenForTeam(team_id);
    }
    if (!botToken) {
      botToken = process.env.SLACK_BOT_TOKEN;
    }

    console.log('🔑 使用Token长度:', botToken ? botToken.length : 0);

    // 验证用户ID是否有效
    const isValidUser = await validateSlackUser(userId, botToken);
    if (!isValidUser) {
      throw new Error(`用户ID无效或不存在: ${userId}`);
    }

    const requestData = { users: userId };
    const requestHeaders = {
      'Authorization': `Bearer ${botToken}`,
      'Content-Type': 'application/json'
    };

    console.log('🔗 Slack API Request - conversations.open:');
    console.log('   URL:', 'https://slack.com/api/conversations.open');
    console.log('   Data:', JSON.stringify(requestData));
    console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });

    const response = await axios.post('https://slack.com/api/conversations.open', requestData, {
      headers: requestHeaders
    });

    console.log('📥 Slack API Response - conversations.open:');
    console.log('   Status:', response.status);
    console.log('   Data:', JSON.stringify(response.data));

    if (response.data.ok) {
      const channelId = response.data.channel.id;
      console.log('✅ 私聊通道打开成功:', channelId);
      return channelId;
    } else {
      // 详细的错误处理
      let errorMessage = `无法打开私聊通道: ${response.data.error}`;

      switch (response.data.error) {
        case 'user_not_found':
          errorMessage += ' - 用户ID不存在或无效';
          console.log('💡 建议: 检查用户ID格式是否正确，确保用户在同一工作区');
          break;
        case 'missing_scope':
          errorMessage += ' - 缺少必要权限';
          console.log('💡 建议: 需要添加 im:write 权限');
          if (response.data.needed) {
            console.log('   需要权限:', response.data.needed);
          }
          if (response.data.provided) {
            console.log('   当前权限:', response.data.provided);
          }
          break;
        case 'user_disabled':
          errorMessage += ' - 用户账户已被禁用';
          break;
        case 'user_not_visible':
          errorMessage += ' - 无权限查看该用户';
          break;
        default:
          if (response.data.needed) {
            errorMessage += ` - 需要权限: ${response.data.needed}`;
          }
          if (response.data.provided) {
            errorMessage += ` - 当前权限: ${response.data.provided}`;
          }
      }

      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error('❌ 打开私聊通道异常:', error.message);
    console.error('🔍 调试信息:');
    console.error('   用户ID:', userId);
    console.error('   团队ID:', team_id || 'null');
    console.error('   Token来源:', botToken === process.env.SLACK_BOT_TOKEN ? '全局Token' : '数据库Token');
    throw error;
  }
}

// 发送Slack消息
async function sendSlackMessage(channel, text, userId = null, team_id = null) {
  const startTime = Date.now();

  console.log('📤 开始发送Slack消息');
  console.log('   参数信息:');
  console.log('     频道ID:', channel);
  console.log('     消息长度:', text.length);
  console.log('     用户ID:', userId || 'null');
  console.log('     团队ID:', team_id || 'null');
  console.log('     开始时间:', new Date().toISOString());

  try {
    // 频道处理和Token选择策略
    let targetChannel = channel;
    let selectedToken = null;
    let tokenSource = '';
    let channelOpenedWithUserToken = false;

    console.log('📺 频道处理流程...');
    console.log('   原始频道ID:', channel);
    console.log('   是否为私信频道:', channel.startsWith('D'));
    console.log('   是否提供用户ID:', !!userId);

    // 如果是私信且提供了用户ID，使用Bot令牌打开私聊通道（Bot与用户的对话）
    if (userId && (channel.startsWith('D') || channel === userId)) {
      console.log('   🤖 检测到私信，Bot将与用户建立对话...');

      try {
        console.log('   🔑 使用Bot令牌打开与用户的私聊通道...');
        const botTokenStartTime = Date.now();
        targetChannel = await openDirectMessage(userId, team_id);
        const botTokenDuration = Date.now() - botTokenStartTime;

        console.log('   ✅ Bot私聊通道打开成功:');
        console.log('     新频道ID:', targetChannel);
        console.log('     耗时:', botTokenDuration + 'ms');
        console.log('     通道类型: Bot与用户的私聊');
        console.log('     消息发送者: Bot（机器人身份）');

        if (targetChannel !== channel) {
          console.log('   📝 频道ID已更新:', channel, '→', targetChannel);
        }
      } catch (botTokenError) {
        console.log('   ❌ Bot私聊通道打开失败:', botTokenError.message);
        console.log('   🔄 继续使用原始频道ID:', channel);
        console.log('   💡 建议: 检查Bot权限配置，确保有im:write权限');
        // 继续使用原始频道ID
      }
    } else {
      console.log('   ⏭️ 跳过私聊通道处理（非私信或无用户ID）');
    }

    // Bot Token选择策略（始终使用Bot令牌发送消息）
    console.log('🤖 开始Bot Token获取流程...');

    if (userId) {
      console.log('   步骤1: 尝试根据用户ID获取Bot Token...');
      try {
        selectedToken = await getBotTokenForUser(userId);
        if (selectedToken) {
          tokenSource = `用户ID(${userId})对应的Bot Token`;
          console.log('   ✅ 根据用户ID获取Bot Token成功');
        } else {
          console.log('   ❌ 根据用户ID未找到Bot Token');
        }
      } catch (userTokenError) {
        console.log('   ❌ 根据用户ID获取Bot Token失败:', userTokenError.message);
      }
    } else {
      console.log('   跳过步骤1: 未提供用户ID');
    }

    if (!selectedToken && team_id) {
      console.log('   步骤2: 尝试根据团队ID获取Bot Token...');
      try {
        selectedToken = await getBotTokenForTeam(team_id);
        if (selectedToken) {
          tokenSource = `团队ID(${team_id})的Bot Token`;
          console.log('   ✅ 根据团队ID获取Bot Token成功');
        } else {
          console.log('   ❌ 根据团队ID未找到Bot Token');
        }
      } catch (teamTokenError) {
        console.log('   ❌ 根据团队ID获取Bot Token失败:', teamTokenError.message);
      }
    } else if (!selectedToken) {
      console.log('   跳过步骤2: 未提供团队ID或已有Token');
    }

    if (!selectedToken) {
      console.log('   步骤3: 使用全局Bot Token作为回退...');
      selectedToken = process.env.SLACK_BOT_TOKEN;
      tokenSource = '全局Bot Token';
      console.log('   ✅ 使用全局Bot Token');
    }

    console.log('🤖 Bot Token选择完成:');
    console.log('   Token来源:', tokenSource);
    console.log('   Token长度:', selectedToken ? selectedToken.length : 0);
    console.log('   Token前缀:', selectedToken ? selectedToken.substring(0, 10) + '...' : 'null');
    console.log('   消息发送者身份: Bot（机器人）');

    // 构建请求数据
    const requestData = {
      channel: targetChannel,
      text: text
    };
    const requestHeaders = {
      'Authorization': `Bearer ${selectedToken}`,
      'Content-Type': 'application/json'
    };

    console.log('📤 准备发送API请求:');
    console.log('   URL: https://slack.com/api/chat.postMessage');
    console.log('   最终频道ID:', targetChannel);
    console.log('   消息内容预览:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
    console.log('   发送者身份: Bot（机器人）');
    console.log('   用户将看到: Bot发送的消息');
    console.log('   请求数据:', JSON.stringify(requestData));
    console.log('   请求头:', { ...requestHeaders, 'Authorization': 'Bearer [BOT_TOKEN_HIDDEN]' });

    // 发送API请求
    const apiStartTime = Date.now();
    console.log('🌐 发送API请求...');

    const response = await axios.post('https://slack.com/api/chat.postMessage', requestData, {
      headers: requestHeaders
    });

    const apiDuration = Date.now() - apiStartTime;
    const totalDuration = Date.now() - startTime;

    console.log('📥 收到API响应:');
    console.log('   HTTP状态:', response.status);
    console.log('   API耗时:', apiDuration + 'ms');
    console.log('   总耗时:', totalDuration + 'ms');
    console.log('   响应数据:', JSON.stringify(response.data));

    if (response.data.ok) {
      console.log('✅ Slack消息发送成功:');
      console.log('   消息时间戳:', response.data.ts);
      console.log('   实际频道:', response.data.channel);
      console.log('   Token来源:', tokenSource);
      console.log('   总处理时间:', totalDuration + 'ms');

      return response.data;
    } else {
      // 详细的错误分析
      console.log('❌ Slack API返回错误:');
      console.log('   错误代码:', response.data.error);
      console.log('   错误详情:', response.data);

      if (response.data.needed) {
        console.log('   需要权限:', response.data.needed);
      }
      if (response.data.provided) {
        console.log('   当前权限:', response.data.provided);
      }

      // 针对不同错误类型提供更详细的错误信息
      let errorMessage = `Slack API错误: ${response.data.error}`;

      switch (response.data.error) {
        case 'channel_not_found':
          errorMessage += ' - Bot可能未被邀请到该频道，或频道不存在';
          console.log('   💡 建议: 检查频道ID是否正确，确保Bot已被邀请到频道');
          break;
        case 'not_in_channel':
          errorMessage += ' - Bot未在该频道中，请先邀请Bot到频道';
          console.log('   💡 建议: 在频道中输入 /invite @your-bot-name');
          break;
        case 'invalid_auth':
          errorMessage += ' - Bot Token无效或已过期';
          console.log('   💡 建议: 检查Token是否正确，是否已过期');
          break;
        case 'missing_scope':
          errorMessage += ' - Bot缺少必要的权限范围';
          console.log('   💡 建议: 在Slack App中添加必要权限并重新安装');
          break;
        default:
          if (response.data.needed) {
            errorMessage += ` - 需要权限: ${response.data.needed}`;
          }
          if (response.data.provided) {
            errorMessage += ` - 当前权限: ${response.data.provided}`;
          }
      }

      console.log('   错误消息:', errorMessage);
      throw new Error(errorMessage);
    }

  } catch (error) {
    const totalDuration = Date.now() - startTime;

    console.error('❌ 发送Slack消息失败:');
    console.error('   错误类型:', error.constructor.name);
    console.error('   错误消息:', error.message);
    console.error('   失败时间:', new Date().toISOString());
    console.error('   总耗时:', totalDuration + 'ms');

    if (error.response) {
      console.error('   HTTP状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }

    if (error.code) {
      console.error('   错误代码:', error.code);
    }

    // 网络错误特殊处理
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      const networkError = '网络连接失败，请检查网络连接';
      console.error('   🌐 网络错误:', networkError);
      throw new Error(networkError);
    }

    // 记录调试信息
    console.error('   🔍 调试信息:');
    console.error('     原始频道:', channel);
    console.error('     目标频道:', targetChannel || 'unknown');
    console.error('     用户ID:', userId || 'null');
    console.error('     团队ID:', team_id || 'null');
    console.error('     Token来源:', tokenSource || 'unknown');

    throw error;
  }
}

// 健康检查端点
app.get('/health', async (req, res) => {
  try {
    // 检查知识图谱状态
    const knowledgeStatus = await knowledgeService.checkKnowledgeBaseStatus();

    // 检查Twilio连接
    let twilioStatus = { success: false, error: 'Not checked' };
    try {
      const account = await twilioClient.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
      twilioStatus = {
        success: true,
        status: account.status,
        type: account.type
      };
    } catch (twilioError) {
      twilioStatus = {
        success: false,
        error: twilioError.message
      };
    }

    // 检查Slack连接
    let slackStatus = { success: false, error: 'Not configured' };
    if (process.env.SLACK_BOT_TOKEN) {
      try {
        const requestHeaders = {
          'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
          'Content-Type': 'application/json'
        };

        console.log('🔍 Slack API Request - auth.test:');
        console.log('   URL:', 'https://slack.com/api/auth.test');
        console.log('   Data:', '{}');
        console.log('   Headers:', { ...requestHeaders, 'Authorization': 'Bearer [HIDDEN]' });

        const slackResponse = await axios.post('https://slack.com/api/auth.test', {}, {
          headers: requestHeaders
        });

        console.log('📥 Slack API Response - auth.test:');
        console.log('   Status:', slackResponse.status);
        console.log('   Data:', JSON.stringify(slackResponse.data));

        if (slackResponse.data.ok) {
          slackStatus = {
            success: true,
            team: slackResponse.data.team,
            user: slackResponse.data.user,
            bot_id: slackResponse.data.bot_id
          };
        } else {
          slackStatus = {
            success: false,
            error: slackResponse.data.error
          };
        }
      } catch (slackError) {
        slackStatus = {
          success: false,
          error: slackError.message
        };
      }
    }

    // 检查LINE连接
    let lineStatus = { success: false, error: 'Not configured' };
    if (process.env.LINE_CHANNEL_ACCESS_TOKEN && lineClient) {
      try {
        // 使用LINE Bot Info API检查连接状态
        const botInfo = await lineClient.getBotInfo();
        lineStatus = {
          success: true,
          botId: botInfo.userId,
          displayName: botInfo.displayName,
          pictureUrl: botInfo.pictureUrl
        };
      } catch (lineError) {
        lineStatus = {
          success: false,
          error: lineError.message
        };
      }
    } else if (!process.env.LINE_CHANNEL_ACCESS_TOKEN) {
      lineStatus = { success: false, error: 'Channel access token not configured' };
    } else if (!lineClient) {
      lineStatus = { success: false, error: 'LINE client not initialized' };
    }

    res.json({
      status: 'healthy',
      service: '畅游网络 WhatsApp、Slack & LINE 智能客服 v2.2',
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      activeSessions: messageProcessor.getUserSessions().total,
      knowledgeGraph: knowledgeStatus,
      twilio: twilioStatus,
      slack: slackStatus,
      line: lineStatus,
      architecture: {
        intentRecognition: '✅ 已启用',
        knowledgeSearch: '✅ 已启用',
        arkAPI: '✅ 已启用',
        memorySystem: '✅ 已启用',
        slackIntegration: process.env.SLACK_BOT_TOKEN ? '✅ 已配置' : '❌ 未配置',
        lineIntegration: process.env.LINE_CHANNEL_ACCESS_TOKEN ? '✅ 已配置' : '❌ 未配置'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});

// 查看活跃会话
app.get('/sessions', (req, res) => {
  const sessions = messageProcessor.getUserSessions();
  res.json(sessions);
});

// 查看工作区鉴权信息
app.get('/slack/workspaces', async (req, res) => {
  try {
    const workspaces = await slackAuthDB.getAllWorkspaces();

    res.json({
      total_workspaces: workspaces.length,
      workspaces: workspaces
    });
  } catch (error) {
    console.error('❌ 获取工作区列表失败:', error.message);
    res.status(500).json({
      error: '获取工作区列表失败',
      message: error.message
    });
  }
});

// 清除用户会话
app.post('/clear-session', (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({
      success: false,
      error: 'Missing userId parameter'
    });
  }

  const cleared = messageProcessor.clearUserSession(userId);

  if (cleared) {
    console.log(`🗑️ 已清除用户 ${userId} 的会话`);
    res.json({
      success: true,
      message: `Session for user ${userId} cleared`
    });
  } else {
    res.status(404).json({
      success: false,
      error: `No session found for user ${userId}`
    });
  }
});

// 意图分析端点（用于调试）
app.post('/analyze-intent', async (req, res) => {
  const { message } = req.body;

  if (!message) {
    return res.status(400).json({
      success: false,
      error: 'Missing message parameter'
    });
  }

  try {
    const IntentRecognitionService = require('./services/intentRecognitionService');
    const intentService = new IntentRecognitionService();

    const intentResult = await intentService.recognizeIntent(message);
    const analysisReport = intentService.getIntentAnalysisReport(intentResult);

    res.json({
      success: true,
      message: message,
      analysis: analysisReport,
      searchQuery: intentService.generateSearchQuery(message, intentResult)
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 知识搜索端点（用于调试）
app.post('/search-knowledge', async (req, res) => {
  const { query } = req.body;

  if (!query) {
    return res.status(400).json({
      success: false,
      error: 'Missing query parameter'
    });
  }

  try {
    const searchResult = await knowledgeService.searchKnowledge(query);
    res.json({
      success: true,
      query: query,
      result: searchResult
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 启动服务器
async function startServer() {
  try {
    console.log('🔥 启动畅游网络智能客服系统 v2.2...');

    // 检查知识图谱状态
    console.log('🧠 检查知识图谱状态...');
    const knowledgeStatus = await knowledgeService.checkKnowledgeBaseStatus();

    if (knowledgeStatus.status !== 'healthy') {
      console.log('⚠️ 知识图谱未初始化，请先运行: node scripts/initializeKnowledgeGraph.js');
    }

    app.listen(PORT, () => {
      console.log('🚀 畅游网络 WhatsApp、Slack & LINE 智能客服 v2.2 启动成功!');
      console.log('=' .repeat(60));
      console.log(`📡 监听端口: ${PORT}`);
      console.log(`🔗 WhatsApp Webhook: http://localhost:${PORT}/whatsapp-webhook`);
      console.log(`💬 Slack Events: http://localhost:${PORT}/slack/events`);
      console.log(`📱 LINE Webhook: http://localhost:${PORT}/line-webhook`);
      console.log(`🔐 Slack OAuth Start: http://localhost:${PORT}/slack/oauth/start`);
      console.log(`🔑 Slack OAuth Callback: http://localhost:${PORT}/slack/oauth/callback`);
      console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
      console.log(`📊 会话查看: http://localhost:${PORT}/sessions`);
      console.log(`🎯 意图分析: http://localhost:${PORT}/analyze-intent`);
      console.log(`🔍 知识搜索: http://localhost:${PORT}/search-knowledge`);
      console.log('');
      console.log('🧠 智能客服系统架构:');
      console.log(`   知识图谱: ${knowledgeStatus.status === 'healthy' ? '✅ 已配置' : '❌ 需要初始化'}`);
      console.log(`   意图识别: ✅ 已启用`);
      console.log(`   ARK API: ✅ 已配置`);
      console.log(`   记忆系统: ✅ 已配置`);
      console.log(`   Slack集成: ${process.env.SLACK_BOT_TOKEN ? '✅ 已配置' : '❌ 未配置'}`);
      console.log(`   LINE集成: ${process.env.LINE_CHANNEL_ACCESS_TOKEN ? '✅ 已配置' : '❌ 未配置'}`);
      console.log('');
      console.log('📱 WhatsApp配置:');
      console.log(`   沙盒号码: +14155238886`);
      console.log('');
      console.log('💬 Slack配置:');
      console.log(`   Bot Token: ${process.env.SLACK_BOT_TOKEN ? '✅ 已配置' : '❌ 未配置'}`);
      console.log(`   Signing Secret: ${process.env.SLACK_SIGNING_SECRET ? '✅ 已配置' : '❌ 未配置'}`);
      console.log('');
      console.log('📱 LINE配置:');
      console.log(`   Channel ID: **********`);
      console.log(`   Channel Secret: ✅ 已配置`);
      console.log(`   Access Token: ${process.env.LINE_CHANNEL_ACCESS_TOKEN ? '✅ 已配置' : '❌ 未配置'}`);
      console.log('');
      console.log('💡 使用ngrok暴露到公网:');
      console.log(`   ngrok http ${PORT}`);
      console.log('   然后在LINE Developers Console中配置Webhook URL:');
      console.log(`   https://your-ngrok-url.ngrok.io/line-webhook`);
      console.log('   在Slack App设置中配置Event Subscriptions URL:');
      console.log(`   https://your-ngrok-url.ngrok.io/slack/events`);
      console.log('');
      console.log('⏳ 等待接收WhatsApp、Slack和LINE消息...');
    });

  } catch (error) {
    console.error('❌ 服务启动失败:', error.message);
    process.exit(1);
  }
}

startServer();

// 优雅关闭
process.on('SIGINT', () => {
  const sessions = messageProcessor.getUserSessions();
  console.log('\n📊 服务器关闭统计:');
  console.log(`   活跃会话: ${sessions.total}`);
  console.log(`   运行时间: ${Math.floor(process.uptime())} 秒`);
  console.log('👋 畅游网络智能客服服务已关闭');
  process.exit(0);
});
