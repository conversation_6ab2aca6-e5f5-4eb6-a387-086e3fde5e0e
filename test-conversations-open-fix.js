// 测试修复后的 conversations.open 功能
require('dotenv').config();
const axios = require('axios');

async function testConversationsOpenFix() {
  console.log('🔧 测试修复后的 conversations.open 功能');
  console.log('=' .repeat(60));

  // 测试不同的用户ID场景
  const testCases = [
    {
      name: '测试原始用户ID',
      userId: 'U0983BEK1HQ',
      description: '使用原始的用户ID进行测试'
    },
    {
      name: '测试已知有效用户ID',
      userId: 'U053CTYEARZ', // 从OAuth回调中获取的用户ID
      description: '使用从OAuth授权中获取的用户ID'
    },
    {
      name: '测试无效用户ID',
      userId: 'U999999999',
      description: '使用明显无效的用户ID测试错误处理'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n${testCase.name}`);
    console.log('=' .repeat(40));
    console.log('描述:', testCase.description);
    console.log('用户ID:', testCase.userId);

    try {
      // 1. 首先测试用户验证
      console.log('\n1️⃣ 测试用户验证...');
      const userValidationResponse = await axios.post('https://slack.com/api/users.info', {
        user: testCase.userId
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📥 用户验证响应:');
      console.log('   状态:', userValidationResponse.status);
      console.log('   成功:', userValidationResponse.data.ok);
      
      if (userValidationResponse.data.ok) {
        console.log('   用户名:', userValidationResponse.data.user.name);
        console.log('   显示名:', userValidationResponse.data.user.profile?.display_name || 'N/A');
        console.log('   真实姓名:', userValidationResponse.data.user.profile?.real_name || 'N/A');
      } else {
        console.log('   错误:', userValidationResponse.data.error);
      }

      // 2. 如果用户验证成功，测试 conversations.open
      if (userValidationResponse.data.ok) {
        console.log('\n2️⃣ 测试 conversations.open...');
        const conversationResponse = await axios.post('https://slack.com/api/conversations.open', {
          users: testCase.userId
        }, {
          headers: {
            'Authorization': `Bearer ${process.env.SLACK_BOT_TOKEN}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('📥 conversations.open 响应:');
        console.log('   状态:', conversationResponse.status);
        console.log('   成功:', conversationResponse.data.ok);
        
        if (conversationResponse.data.ok) {
          console.log('   频道ID:', conversationResponse.data.channel.id);
          console.log('   ✅ 私聊通道打开成功');
        } else {
          console.log('   错误:', conversationResponse.data.error);
          if (conversationResponse.data.needed) {
            console.log('   需要权限:', conversationResponse.data.needed);
          }
          if (conversationResponse.data.provided) {
            console.log('   当前权限:', conversationResponse.data.provided);
          }
        }
      } else {
        console.log('⏭️ 跳过 conversations.open 测试（用户验证失败）');
      }

    } catch (error) {
      console.log('❌ 测试失败:', error.message);
      if (error.response) {
        console.log('   HTTP状态:', error.response.status);
        console.log('   响应数据:', error.response.data);
      }
    }
  }

  // 3. 测试服务器端的修复功能
  console.log('\n3️⃣ 测试服务器端修复功能...');
  console.log('=' .repeat(40));

  const serverPort = process.env.PORT || 3002;
  const mockSlackEvent = {
    type: 'event_callback',
    team_id: 'T059DMNT0SW',
    event: {
      type: 'message',
      user: 'U053CTYEARZ', // 使用已知有效的用户ID
      text: '测试修复后的私聊功能',
      channel: 'D097W5SPBLM',
      ts: Date.now() / 1000
    }
  };

  try {
    console.log('📨 发送模拟事件到服务器...');
    console.log('   用户ID:', mockSlackEvent.event.user);
    console.log('   消息:', mockSlackEvent.event.text);

    const eventResponse = await axios.post(`http://localhost:${serverPort}/slack/events`, mockSlackEvent, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    if (eventResponse.status === 200) {
      console.log('✅ 服务器事件处理成功');
      console.log('   状态码:', eventResponse.status);
      console.log('   服务器应该显示详细的用户验证和私聊通道打开日志');
    } else {
      console.log('❌ 服务器事件处理失败:', eventResponse.status);
    }

  } catch (serverError) {
    console.log('❌ 服务器测试失败:', serverError.message);
    if (serverError.code === 'ECONNREFUSED') {
      console.log('💡 请确保服务器正在运行: npm start');
    }
  }

  console.log('\n📊 修复功能总结');
  console.log('=' .repeat(60));
  console.log('✅ 新增功能:');
  console.log('   1. 用户ID验证 - 调用 users.info 验证用户是否存在');
  console.log('   2. 详细错误处理 - 针对不同错误类型提供具体建议');
  console.log('   3. 调试信息增强 - 记录完整的调试上下文');
  console.log('   4. 权限问题诊断 - 显示需要和当前的权限范围');
  console.log('');
  console.log('🔧 错误处理改进:');
  console.log('   - user_not_found: 用户ID不存在或无效');
  console.log('   - missing_scope: 缺少必要权限（im:write）');
  console.log('   - user_disabled: 用户账户已被禁用');
  console.log('   - user_not_visible: 无权限查看该用户');
  console.log('');
  console.log('💡 使用建议:');
  console.log('   1. 确保用户ID格式正确（以U开头）');
  console.log('   2. 确保用户在同一个工作区');
  console.log('   3. 确保Bot有 im:write 权限');
  console.log('   4. 检查用户账户状态是否正常');
  console.log('');
  console.log('🎯 下一步:');
  console.log('   1. 在Slack App中添加 im:write 权限');
  console.log('   2. 重新安装App到工作区');
  console.log('   3. 使用有效的用户ID进行测试');
  console.log('   4. 观察服务器端的详细日志输出');
}

// 运行测试
testConversationsOpenFix();
