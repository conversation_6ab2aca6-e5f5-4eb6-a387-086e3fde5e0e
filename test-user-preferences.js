// 测试用户偏好记录和个性化推荐功能
require('dotenv').config();
const MessageProcessingService = require('./src/services/messageProcessingService');

async function testUserPreferences() {
  console.log('🧪 测试用户偏好记录和个性化推荐功能...');
  
  const messageProcessor = new MessageProcessingService();
  
  // 模拟用户对话序列
  const conversationFlow = [
    {
      message: '你好',
      description: '初次问候'
    },
    {
      message: '我经常需要出差去美国，一般都是商务差旅，通常3-4个人一起',
      description: '第一次透露偏好信息'
    },
    {
      message: '我比较关注性价比，不需要太贵的产品',
      description: '透露价格敏感度'
    },
    {
      message: '续航时间很重要，因为经常开会用不了充电',
      description: '透露技术偏好'
    },
    {
      message: '现在我又要去欧洲出差了，这次是5个人，有什么推荐吗？',
      description: '基于历史偏好的新咨询'
    }
  ];
  
  const from = 'whatsapp:+1234567890';
  const userName = 'TestUser';
  
  for (let i = 0; i < conversationFlow.length; i++) {
    const { message, description } = conversationFlow[i];
    
    console.log(`\n${'='.repeat(60)}`);
    console.log(`📝 第${i + 1}轮对话: ${description}`);
    console.log(`💬 用户消息: "${message}"`);
    
    try {
      const result = await messageProcessor.processMessage(from, message, userName);
      
      console.log('\n📊 处理结果:');
      console.log(`成功: ${result.success}`);
      console.log(`意图: ${result.intent}`);
      console.log(`置信度: ${result.confidence}`);
      console.log(`使用知识: ${result.usedKnowledge}`);
      
      console.log('\n💬 AI回复:');
      console.log(result.response);
      
      // 检查回复是否体现了个性化
      if (i >= 1) { // 从第二轮开始检查个性化
        const hasPersonalization = 
          result.response.includes('商务') || 
          result.response.includes('美国') || 
          result.response.includes('性价比') || 
          result.response.includes('续航') ||
          result.response.includes('欧洲');
        
        console.log(`🎯 个性化体现: ${hasPersonalization ? '✅ 是' : '⚠️ 否'}`);
      }
      
    } catch (error) {
      console.error('❌ 测试失败:', error.message);
    }
    
    // 添加延迟，模拟真实对话间隔
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // 测试用户档案获取
  console.log(`\n${'='.repeat(60)}`);
  console.log('👤 最终用户档案:');
  try {
    const userId = from.replace('whatsapp:', '');
    const finalProfile = await messageProcessor.getUserProfile(userId);
    console.log(JSON.stringify(finalProfile, null, 2));
  } catch (error) {
    console.error('❌ 获取用户档案失败:', error.message);
  }
}

testUserPreferences();
