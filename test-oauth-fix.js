// 测试OAuth修复后的Token刷新逻辑
require('dotenv').config();
const TokenManager = require('./src/services/tokenManager');
const SlackAuthDatabase = require('./src/database/slackAuth');

async function testOAuthFix() {
  console.log('🧪 测试OAuth修复后的Token刷新逻辑...');
  console.log('=' .repeat(50));

  const tokenManager = new TokenManager();
  const slackAuthDB = new SlackAuthDatabase();

  try {
    // 1. 检查数据库表结构
    console.log('1️⃣ 检查数据库表结构...');
    
    // 获取所有工作区
    const workspaces = await slackAuthDB.getAllWorkspaces();
    console.log(`找到 ${workspaces.length} 个工作区:`);
    
    if (workspaces.length === 0) {
      console.log('❌ 没有找到工作区');
      console.log('💡 请先进行OAuth授权: http://localhost:3002/slack/oauth/start');
      return;
    }

    // 2. 检查第一个工作区的详细信息
    const testWorkspace = workspaces[0];
    console.log(`\n2️⃣ 检查工作区 ${testWorkspace.team_name} 的详细信息...`);
    
    const workspaceDetail = await slackAuthDB.getWorkspaceByTeamId(testWorkspace.team_id);
    
    if (workspaceDetail) {
      console.log('   工作区详细信息:');
      console.log('   - team_id:', workspaceDetail.team_id);
      console.log('   - team_name:', workspaceDetail.team_name);
      console.log('   - bot_user_id:', workspaceDetail.bot_user_id);
      console.log('   - bot_user_access_token:', workspaceDetail.bot_user_access_token ? `存在 (${workspaceDetail.bot_user_access_token.length}字符)` : '不存在');
      console.log('   - bot_refresh_token:', workspaceDetail.bot_refresh_token ? `存在 (${workspaceDetail.bot_refresh_token.length}字符)` : '不存在');
      console.log('   - bot_token_expires_at:', workspaceDetail.bot_token_expires_at || '未设置');
      
      if (workspaceDetail.bot_token_expires_at) {
        const expiresAt = new Date(workspaceDetail.bot_token_expires_at);
        const now = new Date();
        const isExpired = expiresAt <= now;
        const timeUntilExpiry = expiresAt - now;
        
        console.log('   - 过期时间:', expiresAt.toISOString());
        console.log('   - 当前时间:', now.toISOString());
        console.log('   - 是否过期:', isExpired ? '✅ 是' : '❌ 否');
        
        if (!isExpired) {
          console.log('   - 剩余时间:', Math.round(timeUntilExpiry / 1000 / 60), '分钟');
        }
      }
    }

    // 3. 测试TokenManager的智能获取
    console.log(`\n3️⃣ 测试TokenManager的智能获取...`);
    
    const smartToken = await tokenManager.getBotToken(testWorkspace.team_id);
    
    if (smartToken) {
      console.log('   ✅ TokenManager获取成功');
      console.log('   Token长度:', smartToken.length);
      console.log('   Token前缀:', smartToken.substring(0, 10) + '...');
      
      // 4. 测试Token有效性
      console.log('\n4️⃣ 测试Token有效性...');
      const axios = require('axios');
      
      try {
        const authResponse = await axios.post('https://slack.com/api/auth.test', {}, {
          headers: {
            'Authorization': `Bearer ${smartToken}`,
            'Content-Type': 'application/json'
          }
        });

        console.log('   API响应:', {
          ok: authResponse.data.ok,
          error: authResponse.data.error,
          team: authResponse.data.team,
          user: authResponse.data.user,
          bot_id: authResponse.data.bot_id
        });

        if (authResponse.data.ok) {
          console.log('   ✅ Token有效，可以正常使用');
          
          // 5. 测试用户验证功能
          console.log('\n5️⃣ 测试用户验证功能...');
          
          // 获取一个测试用户ID
          const usersResponse = await axios.post('https://slack.com/api/users.list', {
            limit: 5
          }, {
            headers: {
              'Authorization': `Bearer ${smartToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (usersResponse.data.ok && usersResponse.data.members.length > 0) {
            const testUser = usersResponse.data.members.find(user => !user.is_bot && !user.deleted);
            
            if (testUser) {
              console.log('   找到测试用户:', testUser.name, `(${testUser.id})`);
              
              // 测试用户验证
              const userInfoResponse = await axios.post('https://slack.com/api/users.info', {
                user: testUser.id
              }, {
                headers: {
                  'Authorization': `Bearer ${smartToken}`,
                  'Content-Type': 'application/json'
                }
              });

              console.log('   用户验证响应:', {
                ok: userInfoResponse.data.ok,
                error: userInfoResponse.data.error,
                user_name: userInfoResponse.data.user?.name
              });

              if (userInfoResponse.data.ok) {
                console.log('   ✅ 用户验证成功，Token可以正常验证用户');
              } else {
                console.log('   ❌ 用户验证失败:', userInfoResponse.data.error);
              }
            } else {
              console.log('   ⚠️ 未找到合适的测试用户');
            }
          } else {
            console.log('   ❌ 获取用户列表失败:', usersResponse.data.error);
          }
          
        } else {
          console.log('   ❌ Token无效:', authResponse.data.error);
          
          if (authResponse.data.error === 'token_expired') {
            console.log('   🔄 Token已过期，需要刷新');
            
            if (workspaceDetail.bot_refresh_token) {
              console.log('   💡 检测到refresh token，可以尝试刷新');
            } else {
              console.log('   ❌ 没有refresh token，需要重新授权');
              console.log('   💡 请访问: http://localhost:3002/slack/oauth/start');
            }
          }
        }
      } catch (apiError) {
        console.log('   ❌ API调用失败:', apiError.message);
      }
    } else {
      console.log('   ❌ TokenManager获取失败');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
testOAuthFix().then(() => {
  console.log('\n🏁 测试完成');
  console.log('\n💡 如果Token已过期且没有refresh token，请重新进行OAuth授权:');
  console.log('   http://localhost:3002/slack/oauth/start');
  process.exit(0);
}).catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
